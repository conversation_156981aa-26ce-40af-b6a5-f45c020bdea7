$(() => {
  $('link[href="https://recoverit.wondershare.com/assets/app.bundle.css"]').remove();
  const situationTabs = ["#college-student-tab", "#project-manager-tab", "#lawyer-tab", "#financial-analyst-tab", "#architecture-manager-tab"];
  const swiperSituation = new Swiper("#swiper-situation", {
    slidesPerView: 1,
    spaceBetween: 10,
    loop: true,
    effect: "fade",
    autoplay: {
      delay: 3500,
      disableOnInteraction: false,
    },
    pagination: {
      el: ".swiper-pagination",
    },
    on: {
      slideChange: function () {
        const currentSlide = this.realIndex;
        $(situationTabs[currentSlide]).tab("show");

        $(situationTabs[currentSlide].replace("-tab", "-collapse")).collapse("show");
      },
    },
  });
  $(".part-banner .content-right .nav-item").on("click", function () {
    const currentSlide = $(this).index();
    swiperSituation.slideToLoop(currentSlide);
  });

  const swiperMethods = new Swiper("#swiper-methods", {
    slidesPerView: 1,
    spaceBetween: 30,
    loop: true,
    allowTouchMove: false, // 禁止手动滑动
    // autoplay: {
    //   delay: 3000,
    //   disableOnInteraction: false,
    // },
    pagination: {
      el: ".swiper-pagination",
      clickable: true,
    },
    navigation: {
      nextEl: ".part-methods .right-btn",
      prevEl: ".part-methods .left-btn",
    },
    on: {
      slideChange: function () {
        const currentSlide = this.realIndex;
        $(".methods-navigation-item").removeClass("active").eq(currentSlide).addClass("active");
      },
    },
  });
  $(".methods-navigation-item").on("click", function () {
    const currentSlide = $(this).index();
    swiperMethods.slideToLoop(currentSlide);
  });

  if (window.innerWidth > 1280) {
    // 大屏幕使用鼠标悬停效果
    $("#my-card-slider .swiper-slide").on("mouseenter", function () {
      $(this).addClass("is-active").siblings().removeClass("is-active");
    });
  } else {
    // 小屏幕使用轮播
    return new Swiper("#my-card-slider", {
      loop: true,
      slidesPerView: 1,
      spaceBetween: 10,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      pagination: {
        el: ".swiper-pagination",
        clickable: true,
      },
      breakpoints: {
        768: {
          slidesPerView: 2,
          spaceBetween: 20,
        },
        992: {
          slidesPerView: 3,
          spaceBetween: 15,
        },
      },
    });
  }

  if (window.innerWidth > 1279) {
    $(".assetsSwiper .swiper-slide").mouseenter(function () {
      $(this).addClass("active").siblings().removeClass("active");
      $(".assetsSwiper-box").css("--assetIndex", $(this).index());
    });
  } else {
    var assetsSwiper = new Swiper("#assetsSwiper", {
      slidesPerView: 1,
      spaceBetween: 20,
      centeredSlides: true,
      autoplay: {
        delay: 3000,
        disableOnInteraction: false,
      },
      loop: true,
      loopedSlides: 2,
      breakpoints: {
        768: {
          slidesPerView: 1,
          spaceBetween: 20,
          centeredSlides: true,
        },
      },
      pagination: {
        el: ".assetsSwiper-pagination",
        clickable: true,
      },
    });
  }
});
