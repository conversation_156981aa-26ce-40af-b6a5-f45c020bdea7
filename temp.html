<!DOCTYPE html>
<html lang="en">
  <head>
    <!-- Google Tag Manager -->
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-WPNBJKV");
    </script>
    <!-- End Google Tag Manager -->
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width,user-scalable=0,initial-scale=1,maximum-scale=1, minimum-scale=1" />
    <meta
      name="description"
      content="Generate video, images, music & sound with AI. Fast, realistic, fully controllable. Designed for creators, marketers, filmmakers, designers and teams." />
    <title>ToMoviee AI – Your All-in-One AI Creative Studio</title>
    <link rel="canonical" href="https://www.tomoviee.ai/" />
    <link rel="shortcut icon" href="https://www.tomoviee.cn/images/home/<USER>" type="image/x-icon" />
    <link rel="stylesheet" href="https://allstatics.wondershare.cn/neveragain/2019/assets/style/bootstrap-wondershare.min.css" />
    <link rel="stylesheet" href="https://www.wondershare.com/assets/js/swiper7-bundle.min.css" />
    <link href="https://www.wondershare.com/assets/aos/aos.min.css" rel="stylesheet" />
    <style>
      main {
        background: url("https://www.tomoviee.ai/images/home/<USER>") no-repeat center top / 100% auto;
      }

      #tab-swiper1 {
        cursor: pointer;
      }

      .btn-lg {
        height: 4rem;
        line-height: 2rem;
      }

      .btn-action {
        border-color: #723fff !important;
        background-color: #723fff !important;
        /* border: none; */
      }

      .btn-action:hover {
        background: #723fff;
      }

      .tianmu-top {
        height: calc(100vh - 80px);
      }

      .h1title {
        font-size: 64px;
        line-height: 72px;
        font-weight: 700;
        letter-spacing: -1.28px;
        padding-top: 24px;
        padding-bottom: 16;
      }

      .banner-video {
        /* background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/tianmu-bg2.png) no-repeat center / cover;
      overflow: hidden; */
      }

      .banner-video .embed-responsive-custom {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        width: 100%;
        height: 100%;
        transition: all 0.2s linear;
      }

      /* .banner-video .embed-responsive-custom::before {
      padding-top: calc(715 / 1450 * 100%);
    } */

      .banner-video .embed-responsive-custom video {
        object-fit: cover;
        object-position: center;
        filter: brightness(0.5);
      }

      .top-content {
        top: 33%;
        width: 100%;
        left: 0;
        z-index: 2;
        padding-left: 24px;
        padding-right: 24px;
      }

      .bot-content {
        top: 24%;
        width: 100%;
        left: 0;
        z-index: 2;
      }

      .bot-content {
        animation: pop-up 1s ease 1.5s forwards;
        opacity: 0;
        transform: scale(0.1);
      }

      .tianmu-show {
        height: 100vh;
      }

      .tianmu-main {
        /* background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/wang.png) no-repeat center -8px #000; */
      }

      .scroll-icon {
        position: absolute;
        bottom: 7%;
        left: 50%;
        transform: translateX(-50%);
      }

      .arrow {
        position: absolute;
        bottom: 6%;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        animation: moveTop 1s linear infinite;
      }

      .explore-nav,
      .sence-nav {
        /* background: linear-gradient(312.6deg, #1BE4FF -3.21%, #0575E6 79.57%); */
        /* border-radius: 3.4375rem; */
        padding: 1px;
      }

      .explore-tab,
      .nav-tabs {
        /* background: linear-gradient(180deg, #000637 0%, #081026 100%); */
        /* border-radius: 0.75rem; */
        font-size: 1.125rem;
      }

      .tab-intro {
        height: 52px;
        line-height: 52px;
        text-align: center;
        color: rgba(255, 255, 255, 0.6);
        background: #ffffff1a;
        border-radius: 0.75rem;
        margin: 0 6px;
        padding: 0 3rem;
      }

      .tab-intro.active {
        color: #fff;
        background: transparent;
        border: 1px solid rgba(255, 255, 255);
      }

      .tab-intro:hover {
        background: rgba(255, 255, 255, 0.05);
        color: rgba(255, 255, 255, 0.6);
        text-decoration: none;
      }

      .tab-intro:first-child {
        margin: 0 6px 0 0;
      }

      .tab-intro:last-child {
        margin: 0 0 0 6px;
      }

      .ws-video {
        position: relative;
        padding-top: calc(620 / 1050 * 100%);
        width: 1050px;
        max-width: 100%;
      }

      .ws-video video {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: auto;
        object-fit: cover;
        border-radius: 24px;
      }

      .tianmu-scroll .swiper-container .swiper-slide {
        max-width: 1050px;
        opacity: 1;
      }

      .swiper-navigation-box {
        max-width: 1050px;
        position: absolute;
        width: 100%;
        left: 50%;
        transform: translateX(-50%);
        top: 0;
        height: 100%;
        z-index: 5;
      }

      .tianmu-scroll .swiper-container .swiper-slide-active {
        opacity: 1;
      }

      .rounded-8 {
        border-radius: 16px;
      }

      .rounded-12 {
        border-radius: 0.75rem;
      }

      .tiyan {
        box-shadow: 0px 2.8px 40.39px 0px rgba(0, 71, 255, 0.55) !important;
        display: none;
      }

      .tiyan.active {
        display: inline-block;
      }

      .part-album {
        display: flex;
        flex-direction: row;
        /* justify-content: space-evenly; */
        justify-content: center;
        width: 100%;
        /* height: 1080px; */
        overflow: hidden;
        gap: 1rem;
      }

      .album-box1,
      .album-box2,
      .album-box3,
      .album-box4,
      .album-box5 {
        display: flex;
        flex-direction: column;
        width: 20%;
        height: 100%;
      }

      /* .part-album::after {content: '';width: 100%;height: 20%;position: absolute;bottom: 0;left: 0;background-image: linear-gradient(359.71deg, #100000 33.27%, rgba(16, 0, 0, 0) 99.75%);opacity: 0.9;pointer-events: none;pointer-events: none;} */
      .part-album .album-img {
        width: 100%;
        opacity: 0.8;
        margin-bottom: 16px;
        transition: all 1s;
        position: relative;
      }

      .part-album .album-img:hover {
        opacity: 1;
        filter: brightness(1.2) saturate(1.4);
      }

      .part-album .album-box1 .album-img {
        margin-top: 0;
      }

      .album-title {
        position: absolute;
        width: 100%;
        left: 0;
        bottom: 0;
        padding: 0.75rem;
        opacity: 1;
        z-index: 2;
        transform: translateZ(100px);
        /* transition: all 1s linear; */
      }

      .album-btn {
        display: none;
      }

      .part-album .album-img:hover .album-title {
        opacity: 0;
        /* background: linear-gradient(180deg, rgba(84, 123, 145, 0) 10.82%, #547B91 54.96%); */
        /* transition: all 0.5s linear; */
      }

      .part-album .album-box1,
      .part-album .album-box5,
      .part-album .album-box3 {
        animation: rolling 50s linear infinite;
      }

      .part-album .album-box2,
      .part-album .album-box4 {
        /* transform: translateY(50%); */
        animation: rollingReverse 50s linear infinite;
      }

      .color1 {
        color: rgba(255, 255, 255, 0.7);
      }

      .color2 {
        color: rgba(255, 255, 255, 0.5);
      }

      @keyframes rolling {
        from {
          transform: translateY(0);
        }

        to {
          transform: translateY(-50%);
        }
      }

      @keyframes rollingReverse {
        from {
          transform: translateY(-50%);
        }

        to {
          transform: translateY(0);
        }
      }

      @keyframes moveTop {
        0% {
          transform: translateY(-50%) translateY(5px);
        }

        50% {
          transform: translateY(-50%) translateY(0);
        }

        100% {
          transform: translateY(-50%) translateY(-5px);
        }
      }

      .meiti-box {
        width: 100%;
        height: 23.25rem;
        background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/mt-bg.png) no-repeat 0 0;
        background-size: 100% 100%;
        transition: all 0.3s linear;
      }

      .tab-mobile {
        margin: 10px 0 0;
      }

      .mobile-name {
        font-size: 20px;
      }

      .ty-enter {
        padding: 0.375rem 1.5rem 1rem;
        background: linear-gradient(0, rgba(0, 86, 251, 0.32) 27%, rgba(0, 86, 251, 0) 100%);
      }

      .mobile-btn {
        margin-top: 1rem;
        display: block;
      }

      .swiper-pagination-bullet {
        background: rgba(255, 255, 255, 0.3);
        opacity: 1;
      }

      .swiper-pagination-bullet-active {
        background: #fff;
      }

      .swiper-pagination {
        /* bottom: 0; */
      }

      .sence-nav {
        max-width: 400px;
      }

      .nav-tabs a {
        flex: 1;
        color: rgba(255, 255, 255, 0.7);
        margin: 0 3px;
        border-radius: 3.8125rem;
        height: 40px;
        line-height: 40px;
      }

      .nav-tabs a.active,
      .nav-tabs a:hover {
        background: linear-gradient(312.6deg, #1be4ff -3.21%, #0575e6 79.57%);
        text-decoration: none;
        color: #fff;
      }

      .pro-column {
        height: 19.5rem;
        background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/column-bg.png) no-repeat 0 0;
        background-size: 100% 100%;
        width: 100%;
      }

      .pro-logo {
        width: 5rem;
        margin-top: -2.5rem;
      }

      .mw-292 {
        max-width: 292px;
      }

      .pro-enter {
        left: 0;
        bottom: 2rem;
        width: 100%;
      }

      .swiper-button-prev,
      .swiper-button-next {
        width: 56px;
        height: 56px;
        background-size: 56px 56px;
        background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTciIGhlaWdodD0iNTciIHZpZXdCb3g9IjAgMCA1NyA1NyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjgiIGN5PSIyOCIgcj0iMjcuMTI1IiB0cmFuc2Zvcm09Im1hdHJpeCgtMSAwIDAgMSA1Ni4yODc4IDAuMzk5OTk0KSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjc1Ii8+CjxwYXRoIGQ9Ik0zMy41Mzc4IDIwLjUyNUwyMy4wMzc4IDI4LjRMMzMuNTM3OCAzNi4yNzUiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMy41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
        opacity: 0.7;
      }

      .swiper-button-prev:hover {
        background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTciIGhlaWdodD0iNTciIHZpZXdCb3g9IjAgMCA1NyA1NyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjguNzEyMiIgY3k9IjI4LjQiIHI9IjI4IiB0cmFuc2Zvcm09InJvdGF0ZSgtMTgwIDI4LjcxMjIgMjguNCkiIGZpbGw9IndoaXRlIi8+CjxwYXRoIGQ9Ik0zMy45NjIyIDM2LjI3NUwyMy40NjIyIDI4LjRMMzMuOTYyMiAyMC41MjUiIHN0cm9rZT0iYmxhY2siIHN0cm9rZS13aWR0aD0iMy41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
      }

      .swiper-button-next {
        background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTciIGhlaWdodD0iNTciIHZpZXdCb3g9IjAgMCA1NyA1NyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjgiIGN5PSIyOCIgcj0iMjcuMTI1IiB0cmFuc2Zvcm09Im1hdHJpeCgxIDAgMCAtMSAwLjI4Nzg0MiA1Ni40KSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIxLjc1Ii8+CjxwYXRoIGQ9Ik0yMy4wMzc4IDM2LjI3NUwzMy41Mzc4IDI4LjRMMjMuMDM3OCAyMC41MjUiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMy41IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+Cg==);
      }

      .swiper-button-next:hover {
        background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTciIGhlaWdodD0iNTciIHZpZXdCb3g9IjAgMCA1NyA1NyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjguNzEyMiIgY3k9IjI4LjQiIHI9IjI4IiBmaWxsPSJ3aGl0ZSIvPgo8cGF0aCBkPSJNMjMuNDYyMiAyMC41MjVMMzMuOTYyMiAyOC40TDIzLjQ2MjIgMzYuMjc1IiBzdHJva2U9ImJsYWNrIiBzdHJva2Utd2lkdGg9IjMuNSIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
      }

      .base-box {
        background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/base-bg.png) no-repeat 0 0;
        background-size: 100% 100%;
        min-height: 16.875rem;
        transition: all 0.3s linear;
      }

      .line-height-175 {
        line-height: 1.75;
      }

      .tianmu-table {
        width: 100%;
        height: 25.9375rem;
        background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/table-bg.png) no-repeat 0 0;
        background-size: 100% 100%;
      }

      .color-blue {
        color: #c3d8ff;
      }

      .flex-1 {
        flex: 1;
      }

      .enter-1,
      .enter-2 {
        position: absolute;
        left: 0;
        top: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 82px;
        width: 96%;
      }

      .enter-1:hover,
      .enter-2:hover {
        background: linear-gradient(180deg, #1c409b 0%, #006dff 100%);
        color: #fff;
        text-decoration: none;
      }

      .enter-1::before,
      .enter-2:before {
        position: absolute;
        top: 6px;
        right: 6px;
        width: 13px;
        height: 13px;
        background-image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTMiIGhlaWdodD0iMTMiIHZpZXdCb3g9IjAgMCAxMyAxMyIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGcgb3BhY2l0eT0iMC4zIj4KPHBhdGggZD0iTTIgNi41SDEwTTcgM0w5Ljc0ODcgNS43NDg3QzEwLjE2MzYgNi4xNjM2MyAxMC4xNjM2IDYuODM2MzcgOS43NDg3IDcuMjUxM0w3IDEwIiBzdHJva2U9IndoaXRlIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9nPgo8L3N2Zz4K);
        content: "";
        background-size: 100% 100%;
        animation: moveRight 1s linear infinite;
      }

      @keyframes moveRight {
        0% {
          transform: translateX(-3px);
        }

        50% {
          transform: translateX(0);
        }

        100% {
          transform: translateX(3px);
        }
      }

      .enter-2 {
        left: -5%;
        width: 102%;
      }

      .text-line {
        text-decoration: underline;
      }

      .text-line:hover {
        color: #fff;
        font-weight: bold;
      }

      .bg-1 {
        left: 0;
        top: -3%;
      }

      .bg-2 {
        right: 0;
        top: -9%;
      }

      .font-size-30 {
        font-size: 1.875rem;
      }

      .part-bottom .box-border .box.box1 {
        background-image: url(https://images.wondershare.cn/ailab/image2023/home-1/bottom-bg1.png);
      }

      .part-bottom .box-border .box.box1:hover {
        background-image: url(https://images.wondershare.cn/ailab/image2023/home-1/bottom-bg1-active.png);
      }

      .part-bottom .box-border .box.box2 {
        background-image: url(https://images.wondershare.cn/ailab/image2023/home-1/bottom-bg2.png);
      }

      .part-bottom .box-border .box.box2:hover {
        background-image: url(https://images.wondershare.cn/ailab/image2023/home-1/bottom-bg2-active.png);
      }

      .part-bottom .box-border .box:hover a {
        opacity: 1;
      }

      .part-bottom .box-border .box text {
        line-height: 157%;
      }

      .part-bottom .box-border .box .px-26 {
        padding-left: 26px;
        padding-right: 26px;
      }

      .part-bottom .box-border .box a {
        opacity: 0;
      }

      text {
        display: block;
      }

      .btn-blue {
        display: inline-block;
        background: #0056fb;
        transition: all 0.3s;
      }

      .btn-blue:hover {
        background: #004de2;
      }

      .btn {
        font-size: 1.125rem;
        padding: 0.88rem 1.4375rem;
      }

      .base-swiper .swiper-slide {
        width: 330px;
        height: auto;
      }

      .swiper-navigation-box .swiper-button-next {
        right: -100px;
        opacity: 0.7;
      }

      .swiper-navigation-box .swiper-button-prev {
        left: -100px;
        opacity: 0.7;
      }

      .font-size-80 {
        font-size: 5rem;
      }

      .sq-btn {
        min-width: 360px;
        font-size: 1.25rem;
      }

      .content-btn {
        max-width: 360px;
      }

      .position_btn {
        width: 360px !important;
      }

      .pos_bot {
        bottom: 32px;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
      }

      @media (max-width: 575.98px) {
        .sq-btn {
          min-width: 100%;
        }

        .content-btn {
          max-width: 100%;
        }

        .position_btn {
          width: calc(100vw - 48px) !important;
        }

        .pos_bot {
          bottom: 24px;
        }

        h4.pb-1 {
          padding-bottom: 4px !important;
          margin: 0;
          font-size: 20px;
        }
      }

      .swiperTab-pagination {
        --time1: 21s;
        --time2: 14s;
        --time3: 17s;
        --time4: 21s;
        --time5: 14s;
        --time6: 14s;
        --time7: 16s;
      }

      .swiperTab-pagination .swiper-pagination-bullet-active {
        width: 3rem;
        background: rgba(255, 255, 255, 0.3);
        background-image: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 50%, #806fff 50%);
        background-size: 200%;
        background-position-x: 0%;
        border-radius: 50px;
      }

      /* .swiperTab-pagination .swiper-pagination-bullet:nth-child(1).swiper-pagination-bullet-active {
      animation: position var(--time1) linear forwards;
    }

    .swiperTab-pagination .swiper-pagination-bullet:nth-child(2).swiper-pagination-bullet-active {
      animation: position var(--time2) linear forwards;
    }

    .swiperTab-pagination .swiper-pagination-bullet:nth-child(3).swiper-pagination-bullet-active {
      animation: position var(--time3) linear forwards;
    } */

      .video-swiper-pagination .swiper-pagination-bullet-active {
        width: 3rem;
        background: rgba(255, 255, 255, 0.3);
        background-image: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 50%, #806fff 50%);
        background-size: 200%;
        background-position-x: 0%;
        border-radius: 50px;
      }

      .video-swiper-s5-pagination .swiper-pagination-bullet-active {
        width: 3rem;
        background: rgba(255, 255, 255, 0.3);
        background-image: linear-gradient(180deg, #fff, #806fff);
        background-size: 200%;
        background-position-x: 0%;
        border-radius: 50px;
      }

      .img-swiper-s7-pagination .swiper-pagination-bullet-active {
        width: 3rem;
        background: rgba(255, 255, 255, 0.3);
        background-image: linear-gradient(180deg, #fff, #806fff);
        background-size: 200%;
        background-position-x: 0%;
        border-radius: 50px;
      }

      .advantagesSwiper-pagination.swiper-horizontal > .swiper-pagination-bullets,
      .advantagesSwiper-pagination.swiper-pagination-bullets.swiper-pagination-horizontal,
      .advantagesSwiper-pagination {
        width: 15px;
        bottom: 50%;
        transform: translateY(50%);
        right: 68px;
        left: auto;
      }

      .advantagesSwiper-pagination .swiper-pagination-bullet-active {
        height: 3rem;
        background: rgba(255, 255, 255, 0.3);
        /* background-image: linear-gradient(90deg, rgba(255, 255, 255, 1) 50%, #fff 50%); */
        background-image: linear-gradient(180deg, #fff, #806fff);
        background-size: 200%;
        background-position-x: 0%;
        border-radius: 50px;
      }

      .img-swiper-s8-pagination .swiper-pagination-bullet-active {
        width: 3rem;
        background: rgba(255, 255, 255, 0.3);
        /* background-image: linear-gradient(90deg, rgba(255, 255, 255, 1) 50%, #fff 50%); */
        background-image: linear-gradient(180deg, #fff, #806fff);
        background-size: 200%;
        background-position-x: 0%;
        border-radius: 50px;
      }

      @keyframes position {
        0% {
          background-position-x: 0%;
        }

        100% {
          background-position-x: -100%;
        }
      }

      @keyframes arrowMoveDown {
        from {
          transform: translateY(0);
          opacity: 1;
        }

        to {
          transform: translateY(1rem);
          opacity: 0.5;
        }
      }

      .video-container {
        position: relative;
        overflow: hidden;
        background-size: cover;
        transform: scale(1);
      }

      .video-container video {
        position: absolute;
        width: 100%;
        height: 100%;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        object-fit: cover;
      }

      .video-container::before {
        content: "";
        display: block;
        padding-top: var(--ratio, calc(9 / 16 * 100%));
      }

      .video-container1::before {
        padding-top: 100%;
      }

      .album-img .video-container::before {
        width: 310px;
      }

      .album-img .video-container1::before {
        width: 266px;
      }

      .audio-no {
        position: absolute;
        top: 24px;
        left: 648px;
        width: 3rem;
        height: 3rem;
        z-index: 10;
        background-image: url("data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24Z' fill='black' fill-opacity='0.28'/%3E%3Cg clip-path='url(%23clip0_623_4012)'%3E%3Cpath d='M15.8502 19.3249H11.9502C11.1252 19.3249 10.4502 19.9999 10.4502 20.8249V26.2249C10.4502 27.0499 11.1252 27.7249 11.9502 27.7249H15.7752L23.3502 34.5499V32.2249V14.8249V12.6499L21.7002 14.0749L15.8502 19.3249Z' fill='white'/%3E%3Cpath d='M37.1498 19.8497L35.5748 18.1997L31.8248 21.9497L28.0748 18.1997L26.4248 19.8497L30.1748 23.5997L26.4998 27.2747L27.9998 28.9247L31.7498 25.1747L35.4998 28.9247L36.9998 27.2747L33.3998 23.5997L37.1498 19.8497Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_623_4012'%3E%3Crect width='26.7' height='21.9' fill='white' transform='translate(10.4502 12.6499)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
        background-size: 3rem 3rem;
        cursor: pointer;
      }

      .audio-no:hover {
        background-image: url("data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24Z' fill='black' fill-opacity='0.4'/%3E%3Cg clip-path='url(%23clip0_1450_3913)'%3E%3Cpath d='M15.8502 19.3249H11.9502C11.1252 19.3249 10.4502 19.9999 10.4502 20.8249V26.2249C10.4502 27.0499 11.1252 27.7249 11.9502 27.7249H15.7752L23.3502 34.5499V32.2249V14.8249V12.6499L21.7002 14.0749L15.8502 19.3249Z' fill='white'/%3E%3Cpath d='M37.1498 19.8497L35.5748 18.1997L31.8248 21.9497L28.0748 18.1997L26.4248 19.8497L30.1748 23.5997L26.4998 27.2747L27.9998 28.9247L31.7498 25.1747L35.4998 28.9247L36.9998 27.2747L33.3998 23.5997L37.1498 19.8497Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_1450_3913'%3E%3Crect width='26.7' height='21.9' fill='white' transform='translate(10.4502 12.6499)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
      }

      .audio-yes {
        background-image: url("data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='48' height='48' rx='24' fill='black' fill-opacity='0.28'/%3E%3Cg clip-path='url(%23clip0_650_6011)'%3E%3Cpath d='M15.8624 19.3249H11.9624C11.1374 19.3249 10.4624 19.9999 10.4624 20.8249V26.2249C10.4624 27.0499 11.1374 27.7249 11.9624 27.7249H15.7874L23.3624 34.5499V32.2249V14.8249V12.6499L21.7124 14.0749L15.8624 19.3249Z' fill='white'/%3E%3Cpath d='M34.0876 15.2747L33.7126 14.8247L31.9126 16.3247L32.2126 16.7747C33.7126 18.7247 34.4626 21.0497 34.4626 23.5247C34.4626 25.9997 33.6376 28.3997 32.2126 30.3497L31.9126 30.7997L33.6376 32.2997L34.0126 31.8497C35.8126 29.5247 36.7876 26.5997 36.7876 23.5997C36.8626 20.5247 35.8876 17.6747 34.0876 15.2747Z' fill='white'/%3E%3Cpath d='M29.3622 17.8247L27.6372 19.3247L28.0122 19.7747C28.9122 20.8997 29.3622 22.2497 29.3622 23.5997C29.3622 25.0247 28.8372 26.3747 28.0122 27.4247L27.6372 27.8747L29.3622 29.3747L29.7372 28.9247C30.9372 27.4247 31.6872 25.5497 31.6872 23.5997C31.6872 21.6497 31.0122 19.7747 29.7372 18.2747L29.3622 17.8247Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_650_6011'%3E%3Crect width='26.775' height='21.9' fill='white' transform='translate(10.4624 12.6499)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
      }

      .audio-yes:hover {
        background-image: url("data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='48' height='48' rx='24' fill='black' fill-opacity='0.4'/%3E%3Cg clip-path='url(%23clip0_1450_3907)'%3E%3Cpath d='M15.8624 19.3249H11.9624C11.1374 19.3249 10.4624 19.9999 10.4624 20.8249V26.2249C10.4624 27.0499 11.1374 27.7249 11.9624 27.7249H15.7874L23.3624 34.5499V32.2249V14.8249V12.6499L21.7124 14.0749L15.8624 19.3249Z' fill='white'/%3E%3Cpath d='M34.0876 15.2747L33.7126 14.8247L31.9126 16.3247L32.2126 16.7747C33.7126 18.7247 34.4626 21.0497 34.4626 23.5247C34.4626 25.9997 33.6376 28.3997 32.2126 30.3497L31.9126 30.7997L33.6376 32.2997L34.0126 31.8497C35.8126 29.5247 36.7876 26.5997 36.7876 23.5997C36.8626 20.5247 35.8876 17.6747 34.0876 15.2747Z' fill='white'/%3E%3Cpath d='M29.3622 17.8247L27.6372 19.3247L28.0122 19.7747C28.9122 20.8997 29.3622 22.2497 29.3622 23.5997C29.3622 25.0247 28.8372 26.3747 28.0122 27.4247L27.6372 27.8747L29.3622 29.3747L29.7372 28.9247C30.9372 27.4247 31.6872 25.5497 31.6872 23.5997C31.6872 21.6497 31.0122 19.7747 29.7372 18.2747L29.3622 17.8247Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_1450_3907'%3E%3Crect width='26.775' height='21.9' fill='white' transform='translate(10.4624 12.6499)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
      }

      .audio-no-mobile:hover {
        background-image: url("data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24Z' fill='black' fill-opacity='0.4'/%3E%3Cg clip-path='url(%23clip0_1450_3913)'%3E%3Cpath d='M15.8502 19.3249H11.9502C11.1252 19.3249 10.4502 19.9999 10.4502 20.8249V26.2249C10.4502 27.0499 11.1252 27.7249 11.9502 27.7249H15.7752L23.3502 34.5499V32.2249V14.8249V12.6499L21.7002 14.0749L15.8502 19.3249Z' fill='white'/%3E%3Cpath d='M37.1498 19.8497L35.5748 18.1997L31.8248 21.9497L28.0748 18.1997L26.4248 19.8497L30.1748 23.5997L26.4998 27.2747L27.9998 28.9247L31.7498 25.1747L35.4998 28.9247L36.9998 27.2747L33.3998 23.5997L37.1498 19.8497Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_1450_3913'%3E%3Crect width='26.7' height='21.9' fill='white' transform='translate(10.4502 12.6499)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
      }

      .audio-yes-mobile {
        background-image: url("data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='48' height='48' rx='24' fill='black' fill-opacity='0.28'/%3E%3Cg clip-path='url(%23clip0_650_6011)'%3E%3Cpath d='M15.8624 19.3249H11.9624C11.1374 19.3249 10.4624 19.9999 10.4624 20.8249V26.2249C10.4624 27.0499 11.1374 27.7249 11.9624 27.7249H15.7874L23.3624 34.5499V32.2249V14.8249V12.6499L21.7124 14.0749L15.8624 19.3249Z' fill='white'/%3E%3Cpath d='M34.0876 15.2747L33.7126 14.8247L31.9126 16.3247L32.2126 16.7747C33.7126 18.7247 34.4626 21.0497 34.4626 23.5247C34.4626 25.9997 33.6376 28.3997 32.2126 30.3497L31.9126 30.7997L33.6376 32.2997L34.0126 31.8497C35.8126 29.5247 36.7876 26.5997 36.7876 23.5997C36.8626 20.5247 35.8876 17.6747 34.0876 15.2747Z' fill='white'/%3E%3Cpath d='M29.3622 17.8247L27.6372 19.3247L28.0122 19.7747C28.9122 20.8997 29.3622 22.2497 29.3622 23.5997C29.3622 25.0247 28.8372 26.3747 28.0122 27.4247L27.6372 27.8747L29.3622 29.3747L29.7372 28.9247C30.9372 27.4247 31.6872 25.5497 31.6872 23.5997C31.6872 21.6497 31.0122 19.7747 29.7372 18.2747L29.3622 17.8247Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_650_6011'%3E%3Crect width='26.775' height='21.9' fill='white' transform='translate(10.4624 12.6499)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
      }

      .audio-yes-mobile:hover {
        background-image: url("data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Crect width='48' height='48' rx='24' fill='black' fill-opacity='0.4'/%3E%3Cg clip-path='url(%23clip0_1450_3907)'%3E%3Cpath d='M15.8624 19.3249H11.9624C11.1374 19.3249 10.4624 19.9999 10.4624 20.8249V26.2249C10.4624 27.0499 11.1374 27.7249 11.9624 27.7249H15.7874L23.3624 34.5499V32.2249V14.8249V12.6499L21.7124 14.0749L15.8624 19.3249Z' fill='white'/%3E%3Cpath d='M34.0876 15.2747L33.7126 14.8247L31.9126 16.3247L32.2126 16.7747C33.7126 18.7247 34.4626 21.0497 34.4626 23.5247C34.4626 25.9997 33.6376 28.3997 32.2126 30.3497L31.9126 30.7997L33.6376 32.2997L34.0126 31.8497C35.8126 29.5247 36.7876 26.5997 36.7876 23.5997C36.8626 20.5247 35.8876 17.6747 34.0876 15.2747Z' fill='white'/%3E%3Cpath d='M29.3622 17.8247L27.6372 19.3247L28.0122 19.7747C28.9122 20.8997 29.3622 22.2497 29.3622 23.5997C29.3622 25.0247 28.8372 26.3747 28.0122 27.4247L27.6372 27.8747L29.3622 29.3747L29.7372 28.9247C30.9372 27.4247 31.6872 25.5497 31.6872 23.5997C31.6872 21.6497 31.0122 19.7747 29.7372 18.2747L29.3622 17.8247Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_1450_3907'%3E%3Crect width='26.775' height='21.9' fill='white' transform='translate(10.4624 12.6499)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
      }

      .tianmu-logo {
        height: 40px;
      }

      @media (max-width: 2100px) {
        .swiperTab-pagination {
          bottom: 5% !important;
        }
      }

      @media (min-width: 768px) {
        .base-swiper .swiper-slide:not(:last-child) {
          margin-right: 1.875rem;
        }

        .meiti-box:hover,
        .base-box:hover {
          transform: translateY(-10px);
        }

        .tianmu-action {
          background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/bg-3.jpg?ivm-quality=OD) no-repeat center 0;
          background-size: cover;
        }
      }

      @media (min-width: 1280px) {
        .section-8 {
        }

        .top-all {
          height: 200vh;
          background: #000;
        }

        .top-sticky {
          position: sticky;
          /* top: 72px; */
        }
      }

      @media (min-width: 2100px) {
        .tianmu-action {
          background-size: 100% 100%;
        }
      }

      @media (max-width: 1600px) {
        .tianmu-scroll .swiper-container .swiper-slide,
        .swiper-navigation-box {
          max-width: 860px;
        }

        .swiper-button-next {
          right: -70px;
        }

        .swiper-button-prev {
          left: -70px;
        }

        .part-bottom .container {
          max-width: 1200px;
        }

        .part-bottom .box-border .box {
          padding: 32px;
        }

        .base-box .color1 {
          font-size: 14px;
        }

        .base-swiper .swiper-slide {
          width: 270px;
        }

        .font-size-80 {
          font-size: 50px;
        }

        .swiperTab-pagination {
          bottom: 2% !important;
        }
      }

      @media (max-width: 1279px) {
        .tianmu-top {
          height: calc(100vh - 48px);
        }

        .banner-video .embed-responsive-custom {
          width: 100%;
          height: 100%;
        }

        /*.banner-video .embed-responsive-custom::before {*/
        /*  padding-top: calc(760 / 340 * 100%);*/
        /*}*/

        .h1 {
          font-size: 36px;
        }

        .tianmu-scroll .swiper-container .swiper-slide,
        .swiper-navigation-box {
          max-width: 720px;
        }

        .part-album::after {
          pointer-events: none;
        }

        .meiti-box {
          height: 20rem;
        }

        .tab-intro {
          font-size: 14px;
        }

        .pro-enter {
          bottom: 1rem;
        }

        .tab-intro {
          flex: 1;
        }

        .tab-intro:last-child {
          min-width: 160px;
          padding: 0px;
        }

        .base-swiper .swiper-slide {
          width: 48%;
        }

        .base-swiper .swiper-slide:nth-child(2) {
          margin-right: 0;
        }

        .base-swiper .swiper-slide:nth-child(3),
        .base-swiper .swiper-slide:nth-child(4) {
          margin-top: 16px;
        }

        .earth {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          object-fit: cover;
          object-position: bottom;
          pointer-events: none;
          transform: translateY(0);
        }

        .album-img .video-container::before {
          width: 374px;
          height: 210px;
        }

        .album-img .video-container1::before {
          width: 210px;
          height: 210px;
        }

        .banner-video .embed-responsive-custom video {
          border-radius: 0rem;
        }
      }

      @media (max-width: 1023px) {
        .tianmu-scroll .swiper-container .swiper-slide,
        .swiper-navigation-box {
          max-width: 540px;
        }

        .act-icon {
          width: 80px;
        }

        .tianmu-table .flex-1 {
          font-size: 14px;
        }

        .tianmu-table .font-size-super {
          font-size: 16px;
        }

        .part-bottom .box-border .box a {
          opacity: 1;
        }
      }

      @media (max-width: 768px) {
        .tianmu-main {
          /* background: #020230; */
        }

        .tianmu-top {
          height: calc(93vh - 48px);
        }

        .tab-intro-mobile {
          height: 52px;
          line-height: 1;
          text-align: center;
          color: rgba(255, 255, 255, 0.6);
          background: transparent;
          border-radius: 0rem;
          margin: 0 6px;
          padding: 0rem;
          flex: 1;
          text-decoration: none;
        }

        .tab-intro-mobile.active,
        .tab-intro-mobile:hover {
          /* background: linear-gradient(312.6deg, #1BE4FF -3.21%, #0575E6 79.57%); */
          color: #fff;
          background: transparent;
          border: none;
          text-decoration: none;
          border-bottom: 2px solid #806fff;
        }

        .tianmu-scroll .swiper-container .swiper-slide video {
          object-fit: cover;
          /* border-radius: 1.25rem 1.25rem 0 0 */
        }

        .tianmu-logo {
          height: 24px;
        }

        .slogon {
          width: 100%;
        }

        .top-content .font-size-extra {
          font-size: 14px;
          letter-spacing: 2px;
        }

        .card-content .font-size-extra {
          font-size: 18px;
          line-height: 26px;
        }

        .card-ai .card-content .card-desc {
          min-height: 120px;
        }

        .people {
          width: 48px;
          bottom: 4%;
        }

        .earth-mobile {
          position: absolute;
          width: 100%;
          left: 0;
          bottom: 0;
        }

        .top-content {
          top: 30%;
        }

        .h1 {
          font-size: 26px;
        }

        .font-size-super {
          font-size: 16px;
        }

        .pro-column {
          height: 18.5rem;
          padding: 0 24px !important;
        }

        .part-bottom .box-border .box {
          padding: 16px 24px;
        }

        .tianmu-scroll .swiper-container .swiper-slide {
          /* max-width: 350px; */
          max-width: calc(100% - 48px);
        }

        .mobile-slide {
          border-radius: 12px;
          /* background: #181840; */
          overflow: hidden;
        }

        .nav-tabs a {
          font-size: 16px;
        }

        .act-icon {
          width: 64px;
          height: 64px;
        }

        .meiti-box {
          padding: 1.5rem 2rem !important;
          background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/mt-mobile-bg.png) no-repeat 0 0;
          background-size: 100% 100%;
          height: 151px;
        }

        .meiti-box .font-size-super {
          font-size: 20px;
        }

        .meiti-box .color1 {
          font-size: 14px;
        }

        .pro-column .font-size-super {
          font-size: 18px;
        }

        .tianmu-tabl .color-blue {
          font-size: 12px;
        }

        #tab-swiper5 .swiper-slide {
          width: 300px;
        }

        .base-swiper .swiper-slide:nth-child(3),
        .base-swiper .swiper-slide:nth-child(4) {
          margin-top: 0;
        }

        .base-box .font-size-super {
          font-size: 18px;
        }

        .tianmu-action {
          background: url(https://images.wondershare.cn/ailab/images2024/tm/homepage/bg-3.jpg) no-repeat center 0;
          background-size: cover;
        }

        #nav-tabContent .swiper-slide {
          max-width: 300px;
        }

        .part-album .album-img {
          opacity: 1;
          filter: brightness(1.2) saturate(1.4);
        }

        .tianmu-link1,
        .tianmu-link2,
        .tianmu-link3,
        .tianmu-link4,
        .tianmu-link5,
        .tianmu-link6,
        .tianmu-link7 {
          text-indent: -99999px;
          position: absolute;
          top: 16%;
          width: 33%;
          height: 6%;
          left: 0;
        }

        .tianmu-link2 {
          left: 66%;
        }

        .tianmu-link3 {
          left: 0;
          top: 40.5%;
        }

        .tianmu-link4 {
          left: 33%;
          top: 40.5%;
        }

        .tianmu-link5 {
          left: 66%;
          top: 40.5%;
        }

        .tianmu-link6 {
          left: 0;
          top: 64.8%;
        }

        .tianmu-link7 {
          left: 33%;
          top: 64.8%;
        }

        .font-size-80 {
          font-size: 36px;
        }

        .album-title {
          opacity: 0;
          padding: 0.75rem 0.375rem 0.375rem;
        }

        /* .album-img .video-container::before {
        width: 250px;
        height: 140px;
      }

      .album-img .video-container1::before {
        width: 140px;
        height: 140px;
      } */

        .part-album .album-box5,
        .part-album .album-box1,
        .part-album .album-box3 {
          animation: rolling 75s linear infinite;
        }

        .part-album .album-box4,
        .part-album .album-box2 {
          transform: translateX(50%);
          animation: rollingReverse 75s linear infinite;
        }
      }
    </style>
    <style>
      .top-content {
        /* animation: name duration timing-function delay iteration-count direction fill-mode; */
        animation: pop-up 1s ease 1.5s forwards;
        opacity: 0;
        transform: scale(0.1);
        /* color: white; */
      }

      @keyframes pop-up {
        0% {
          opacity: 0;
          transform: scale(0.1);
        }

        100% {
          opacity: 1;
          transform: scale(1);
        }
      }

      .font-size-72 {
        font-size: 72px;
      }

      .subtitle {
        line-height: 56px;
      }

      .mb-36 {
        margin-bottom: 36px;
      }

      .subdisc {
        color: rgba(255, 255, 255, 0.6);
        max-width: 360px;
      }

      .h5sub {
        max-width: 100%;
      }

      .line-height-25 {
        line-height: 28px;
      }

      .line-height-32 {
        line-height: 32px;
      }

      .line-height-28 {
        line-height: 22px;
      }

      .line-height-3rem {
        line-height: 40px;
      }

      .line-height-4rem {
        line-height: 56px;
      }

      .line-height-2rem {
        line-height: 28px;
      }

      .text-color-gradient {
        background: linear-gradient(0deg, rgba(255, 255, 255, 0.2) 0%, #ffffff 96.12%), linear-gradient(0deg, #806fff, #806fff);
        -webkit-background-clip: text;
        background-clip: text;
        color: transparent;
      }

      .img_text {
        vertical-align: middle;
      }

      .star2 {
        width: 1.75rem;
        vertical-align: middle;
      }

      .lazy-video {
        object-fit: cover;
        object-position: center;
      }

      .fade-up-anim {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.6s ease, transform 0.6s ease;
      }

      .fade-up-anim.in-view {
        opacity: 1;
        transform: translateY(0);
      }

      .fade-up-anim.out-view {
        opacity: 0;
        transform: translateY(-20px);
        /* 向上淡出 */
      }

      .border-radus-24 {
        border-radius: 1.5rem;
      }

      .card-ai-box {
        background-color: #ffffff0d;
        padding: 6px;
        border-radius: 1.5rem;
      }

      .card-ai {
        background: rgba(0, 0, 0, 0.85);
        border-radius: 1.2rem;
      }

      .card-ai img {
        width: 100%;
        border-radius: 1.5rem;
      }

      .card-ai .card-content {
        padding: 0;
      }

      .card-ai .card-content .card-desc {
        color: #ffffff99;
        line-height: 22px;
        min-height: 142px;
      }
      @media (max-width: 1600px) {
        .card-ai .card-content .card-desc {
          min-height: 164px;
        }
      }
      @media (max-width: 768px) {
        .card-ai {
          background: rgba(255, 255, 255, 0);
          border-radius: 12px;
        }

        .font-size-72 {
          font-size: 36px;
        }

        .mb-36 {
          margin-bottom: 6px;
        }

        .card-ai .card-content .card-desc {
          color: #ffffff99;
          line-height: 22px;
        }
      }
    </style>
    <!-- 屏内滑动 -->
    <style>
      .part-process .common-tab {
        list-style: none;
        padding: 0;
        margin: 0 -0.3125rem;
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        position: relative;
      }

      .part-process .common-tab li {
        width: calc(50% - 0.625rem);
        padding: 1rem 0.75rem;
        margin: 0.3125rem;
        background-color: #2c3035;
        font-size: 1.125rem;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.5);
        border-radius: 0.5rem;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
      }

      .part-process .common-tab li .wsc-icon {
        height: 1.5rem;
      }

      .part-process .common-tab li .wsc-icon svg {
        height: 100%;
      }

      .part-process .common-tab li span {
        padding-left: 0.5rem;
      }

      .part-process .common-tab li.active {
        background-color: #1b615b;
        color: #fff;
        font-weight: 700;
      }

      .part-process .swiper {
        overflow: initial;
        pointer-events: none;
      }

      .part-process .video-container {
        width: 100%;
        --ratio: calc(496 / 880 * 100%);
        transform: scale(1.064);
        transform-origin: left bottom;
        margin-top: 3.5%;
      }

      @media (any-hover) {
        .part-process .common-tab li:hover {
          background-color: #1b615b;
          color: #fff;
          font-weight: 700;
        }
      }

      @media (min-width: 1280px) {
        #mobileQrcodeModal .modal-dialog {
          min-width: 690px;
        }

        .part-process {
          height: 450vh;
          --nowIndex: 1;
        }

        .part-process .process-container {
          position: sticky;
          top: -98px;
          overflow: hidden;
        }

        .part-process .process-wrapper .process-slide {
          pointer-events: none;
        }

        .part-process .process-wrapper .process-slide h3.font-size-42 {
          line-height: 1;
        }

        /* 测试 */
        .part-process .process-wrapper .process-slide .box-left {
          will-change: transform;
          transform-style: preserve-3d;
          transition: opacity 0.5s ease-out, visibility 0.5s ease-out, transform 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
          opacity: 0;
          visibility: hidden;
          transform: translate3d(0px, 100px, 0px);
        }

        .part-process .process-wrapper .process-slide.active {
          pointer-events: auto;
        }

        .part-process .process-wrapper .process-slide.active .box-left {
          transition-delay: 0.3s;
          visibility: visible;
          opacity: 1;
          transform: translate3d(0px, 0px, 0px);
        }

        .part-process .process-wrapper .process-slide:not(.active) .box-left {
          opacity: 0;
          visibility: hidden;
          transform: translate3d(0px, -100px, 0px);

          transition: opacity 0.5s ease-out, visibility 0.5s ease-out, transform 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
        }

        .part-process .process-wrapper .process-slide .video-box-right {
          will-change: transform;
          transform-style: preserve-3d;
          visibility: hidden;
          opacity: 0;
          transform: translate3d(0px, 100px, 50px) scale3d(0.9, 0.9, 1);
          transition: opacity 0.5s ease-out, visibility 0.5s ease-out, transform 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
        }

        .part-process .process-wrapper .process-slide.active .video-box-right {
          transition-delay: 0.2s;
          transform: translate3d(0px, 0px, 0px) scale3d(1, 1, 1);
          visibility: visible;
          opacity: 1;
        }

        .part-process .process-wrapper .process-slide:not(.active) .video-box-right {
          visibility: hidden;
          opacity: 0;
          transform: translate3d(0px, -100px, -50px) scale3d(0.9, 0.9, 1);

          transition: opacity 0.5s ease-out, visibility 0.5s ease-out, transform 0.8s cubic-bezier(0.05, 0.61, 0.41, 0.95);
        }

        .part-process .process-wrapper .process-slide:not(:first-child) {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
        }

        /* 测试 */

        .part-process .process-wrapper .process-slide .box-left {
          max-width: 520px;
        }

        .part-process .swiper-pagination {
          display: none;
        }

        .part-process .process-progress {
          position: absolute;
          top: 50%;
          right: 1rem;
          transform: translateY(-50%);
          width: 0.5rem;
        }

        .process-progress .process-progress-box {
          display: flex;
          align-items: center;
          flex-direction: column;
          gap: 8px;
        }

        .part-process .swiper-progress-bar {
          width: 0.5rem;
          height: 0.5rem;
          background: #ffffff4d;
          border-radius: 0.5rem;
        }

        .process-progress-box {
          --time11: 10s;
          --time21: 10s;
          --time31: 10s;
          --time41: 10s;
        }

        .process-progress-box .swiper-progress-bar-active {
          height: 3rem;
          background-color: rgba(255, 255, 255, 0.3);
          background-image: linear-gradient(180deg, #fff, #806fff);
          background-size: 100% 200%;
          background-position-y: 0%;
          background-repeat: no-repeat;
          border-radius: 8px;
        }

        /* .process-progress-box .swiper-progress-bar:nth-child(1).swiper-progress-bar-active {
        animation: position2 var(--time11) linear forwards;
      }

      .process-progress-box .swiper-progress-bar:nth-child(2).swiper-progress-bar-active {
        animation: position2 var(--time21) linear forwards;
      }

      .process-progress-box .swiper-progress-bar:nth-child(3).swiper-progress-bar-active {
        animation: position2 var(--time31) linear forwards;
      }

      .process-progress-box .swiper-progress-bar:nth-child(4).swiper-progress-bar-active {
        animation: position2 var(--time41) linear forwards;
      } */

        @keyframes position2 {
          0% {
            background-position-y: 200%;
          }

          100% {
            background-position-y: 100%;
          }
        }

        .part-process .swiper-slide {
          opacity: 0 !important;
        }

        .part-process .swiper-slide-active {
          opacity: 1 !important;
        }
      }

      @media (min-width: 1366px) {
        .part-process .process-container {
          top: 88px;
        }
      }

      @media (min-width: 1400px) {
        .part-process .process-container {
          top: 14vh;
        }
      }

      @media (min-width: 1600px) {
        .part-process .process-container {
          top: 145px;
        }
      }

      @media (min-width: 2000px) {
        .part-process .process-container {
          top: 20vh;
        }
      }

      @media (max-width: 1399.98px) {
        .part-process .video-container {
          transform: scale(1);
          margin-top: 0;
        }
      }

      @media (max-width: 1279.98px) {
        .part-process {
          overflow: hidden;
        }

        .part-process .video-container {
          border-radius: 1rem !important;
        }

        .part-process .video-container video {
          border-radius: 1rem !important;
        }

        .part-process .common-tab {
          display: none;
        }

        .part-process .swiper {
          overflow: initial;
          padding-top: 16px;
          padding-bottom: 28px;
          pointer-events: initial;
        }

        .part-process .process-wrapper .process-slide {
          width: 60%;
          margin: 0 auto;
          margin-bottom: 64px;
        }

        .part-process .process-wrapper .process-slide .swiper-slide {
          background-color: #1e2125;
          border-radius: 15px;
          padding: 12px 12px 20px 12px;
          overflow: hidden;
          opacity: 0.5;
          font-size: 14px;
          height: auto;
        }

        .part-process .process-wrapper .process-slide .swiper-slide-active {
          z-index: 3;
          opacity: 1;
        }

        .part-process .process-wrapper .process-slide .box-title {
          font-size: 16px;
          line-height: 1;
          font-weight: 700;
          color: #fff;
          padding-top: 16px;
        }

        .part-process .process-wrapper .process-slide .box-skip {
          display: inline-block;
          color: #50e3c2;
          font-size: 12px;
          line-height: 1;
          padding-top: 7px;
          font-weight: 400;
          text-decoration: none;
        }
      }

      @media (max-width: 575.98px) {
        .part-process .process-wrapper .process-slide {
          width: 77.8%;
        }

        .part-process .process-wrapper .process-slide .swiper-slide {
          display: flex;
          flex-direction: column;
        }

        .part-process .process-wrapper .process-slide .swiper-slide .video-container {
          flex-shrink: 0;
        }

        .part-process .process-wrapper .process-slide .box-style .box-left {
          padding: 0 22px;
        }
      }
    </style>
    <!-- 插屏滚动 -->
    <style>
      #partAlbum {
        position: relative;
        max-height: 1288px;
        max-width: 1624px;
        margin: auto;
      }

      .mask_top {
        position: absolute;
        top: -4px;
        z-index: 6;
        width: 100%;
        height: 20%;
        background: linear-gradient(360deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.9) 80%, rgb(0, 0, 0));
        transform: translateZ(0);
      }

      .mask_bottom {
        position: absolute;
        bottom: -4px;
        z-index: 6;
        width: 100%;
        height: 20%;
        background: linear-gradient(180deg, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.9) 80%, rgb(0, 0, 0));
        transform: translateZ(0);
      }

      .mask_btn_box {
        position: absolute;
        bottom: 4rem;
        z-index: 7;
        left: 50%;
        transform: translateX(-50%) !important;
      }

      .mask_mao_top {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 5;
        width: 100%;
        height: 20%;
        transform: scaleY(-1) translateZ(0);
      }

      .mask_mao_top div:first-of-type {
        z-index: 1;
        -webkit-backdrop-filter: blur(0);
        backdrop-filter: blur(0);
        -webkit-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 0, rgb(0, 0, 0) 25%);
        mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 0, rgb(0, 0, 0) 25%);
      }

      .mask_mao_top div:nth-of-type(2) {
        z-index: 2;
        -webkit-backdrop-filter: blur(4px);
        backdrop-filter: blur(4px);
        -webkit-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 25%, rgb(0, 0, 0) 50%);
        mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 25%, rgb(0, 0, 0) 50%);
      }

      .mask_mao_top div:nth-of-type(3) {
        z-index: 3;
        -webkit-backdrop-filter: blur(8px);
        backdrop-filter: blur(8px);
        -webkit-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 50%, rgb(0, 0, 0) 75%);
        mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 50%, rgb(0, 0, 0) 75%);
      }

      .mask_mao_top div:nth-of-type(4) {
        z-index: 4;
        -webkit-backdrop-filter: blur(12px);
        backdrop-filter: blur(12px);
        -webkit-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 75%, rgb(0, 0, 0));
        mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 75%, rgb(0, 0, 0));
      }

      .mask_mao_bot {
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 5;
        width: 100%;
        height: 20%;
      }

      .mask_mao_bot div:first-of-type {
        z-index: 1;
        -webkit-backdrop-filter: blur(0);
        backdrop-filter: blur(0);
        -webkit-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 0, rgb(0, 0, 0) 25%);
        mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 0, rgb(0, 0, 0) 25%);
      }

      .mask_mao_bot div:nth-of-type(2) {
        z-index: 2;
        -webkit-backdrop-filter: blur(4px);
        backdrop-filter: blur(4px);
        -webkit-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 25%, rgb(0, 0, 0) 50%);
        mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 25%, rgb(0, 0, 0) 50%);
      }

      .mask_mao_bot div:nth-of-type(3) {
        z-index: 3;
        -webkit-backdrop-filter: blur(8px);
        backdrop-filter: blur(8px);
        -webkit-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 50%, rgb(0, 0, 0) 75%);
        mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 50%, rgb(0, 0, 0) 75%);
      }

      .mask_mao_bot div:nth-of-type(4) {
        z-index: 4;
        -webkit-backdrop-filter: blur(12px);
        backdrop-filter: blur(12px);
        -webkit-mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 75%, rgb(0, 0, 0));
        mask: linear-gradient(180deg, rgba(0, 0, 0, 0) 75%, rgb(0, 0, 0));
      }

      .mask_mao_top > div,
      .mask_mao_bot > div {
        position: absolute;
        inset: 0;
      }

      .part-scroll-animation {
        position: relative;
      }

      .part-scroll-animation .content-box {
        position: relative;
        z-index: 9;
      }

      .flex-container {
        display: flex;
        flex-wrap: nowrap;
        margin: 0 -4px;
      }

      .flex-container .item {
        flex: 0 0 auto;
        padding: 0 4px;
        position: relative;
      }

      .flex-container .col-1\/6 {
        width: calc(100% / 6);
      }

      .flex-container .col-3\/6 {
        width: calc(50%);
      }

      .flex-container .col-2\/6 {
        width: calc(100% / 3);
      }

      .btn-gradient {
        display: inline-flex;
        align-items: center;
        font-weight: 700;
        color: #fff;
        font-size: 14px;
        padding: 8px 20px;
        background: linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 100%),
          linear-gradient(180deg, rgba(255, 255, 255, 0.46) -3.54%, rgba(255, 255, 255, 0) 96.32%),
          linear-gradient(270deg, #ffd600 -9.28%, #ff0e0e 30.8%, #bd00ff 63.88%, #30f 99.4%);
      }

      .btn-gradient:hover {
        color: #fff;
      }

      .btn-gradient.arrow::after,
      .btn.arrow::after {
        content: "";
        display: inline-block;
        align-items: center;
        width: 14px;
        height: 12px;
        margin-left: 8px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='13' viewBox='0 0 14 13' fill='none'%3E%3Cpath d='M7.32505 1.51136L12.3137 6.5M12.3137 6.5L7.32506 11.4887M12.3137 6.5L1 6.5' stroke='white' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
      }

      @media (min-width: 992px) {
        .flex-container {
          margin: 0 -15px;
        }

        .flex-container .item {
          padding: 0 15px;
        }

        .flex-container .with-scroll {
          transform: translateY(var(--y, 0));
          transition: transform 0.5s;
        }
      }
    </style>
    <!-- 动效边框 -->
    <style>
      .section-8 {
        height: 1180px;
        background-image: url("https://www.tomoviee.ai/images/home/<USER>");
        background-repeat: no-repeat;
        background-position: top center;
        background-size: cover;
      }

      .section-5 {
        margin-top: -40px;
      }

      .for_bg {
        background-image: url("https://www.tomoviee.ai/images/home/<USER>");
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100%;
      }

      .shadow4 {
        box-shadow: 0px 0px 0px 4px rgba(255, 255, 255, 0.02);
      }

      .ai_left {
        height: 100%;
        /* background-image: url("https://www.tomoviee.ai/images/home/<USER>"); */
        /* background-repeat: no-repeat;
      background-position: center top;
      background-size: 100%; */
        /* border: 0.5rem solid rgba(255, 255, 255, 0.09); */
        /* border-radius: 2rem; */
        /* box-shadow: 0px 0px 0px 0.25rem rgba(255, 255, 255, 0.02); */
      }

      .ai_top {
        width: 100%;
        height: calc(50% - 15px);
        /* background-image: url("https://www.tomoviee.ai/images/home/<USER>");
      background-repeat: no-repeat;
      background-position: right center;
      background-size: 100%; */
        /* border: 0.5rem solid rgba(255, 255, 255, 0.09); */
        /* border-radius: 2rem; */
        /* box-shadow: 0px 0px 0px 0.25rem rgba(255, 255, 255, 0.02); */
        margin-bottom: 15px;
      }

      .ai_bottom {
        width: 100%;
        height: 50%;
      }

      .ai_br {
        /* border: 0.5rem solid rgba(255, 255, 255, 0.09); */
        /* border-radius: 2rem; */
        height: 100%;
        /* box-shadow: 0px 0px 0px 0.25rem rgba(255, 255, 255, 0.02); */
        margin-top: 15px;
      }

      .ai_bl {
        /* border: 0.5rem solid rgba(255, 255, 255, 0.09); */
        /* border-radius: 2rem; */
        height: 100%;
        /* box-shadow: 0px 0px 0px 0.25rem rgba(255, 255, 255, 0.02); */
        margin-top: 15px;
      }

      .ai_mobile {
        /* border: 0.5rem solid rgba(255, 255, 255, 0.09); */
        border-radius: 2rem;
        min-height: 242px;
        /* box-shadow: 0px 0px 0px 0.25rem rgba(255, 255, 255, 0.02); */
        margin-top: 15px;
        text-align: center;
        border-radius: 16px;
        border: 2px solid rgba(255, 255, 255, 0.2);
        background: linear-gradient(122deg, rgba(0, 0, 0, 0) 39.5%, rgba(147, 52, 255, 0.15) 104.8%),
          radial-gradient(73.4% 76.51% at 97.71% 1.69%, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%), #04000c;
      }

      .ai_mobile_content {
        text-align: center;
        padding: 32px 10px;
      }

      .ai_mobile_content .subdisc {
      }

      .ai_left_content {
        height: 50%;
        padding-left: 102px;
      }

      .ai_br_content {
        padding-left: 60px;
        max-width: 330px;
      }

      .ai_bl_content {
        padding-left: 80px;
        max-width: 330px;
      }

      .ai_top_content {
        width: 50%;
        padding-left: 80px;
        max-width: 330px;
        padding-top: 36px;
      }

      .ai_box_left {
        min-height: 750px;
      }

      .ai_box_right {
        min-height: 750px;
      }

      .ai_color_box {
        min-height: 750px;
        box-sizing: border-box;
        padding-left: 0px !important;
      }

      @media (max-width: 1599.98px) {
        .section-8 {
          height: 1030px;
        }

        .ai_color_box {
          min-height: 600px;
          box-sizing: border-box;
          padding-left: 0px !important;
        }

        .ai_left_content {
          height: 50%;
          padding-left: 60px;
        }

        .ai_br_content {
          padding-left: 32px;
          max-width: 300px;
        }

        .ai_bl_content {
          padding-left: 47px;
          max-width: 300px;
        }

        .ai_top_content {
          width: 50%;
          padding-left: 47px;
          max-width: 320px;
        }

        .ai_box_left {
          min-height: 600px;
        }

        .ai_box_right {
          min-height: 600px;
        }
      }

      @media (max-width: 1399.98px) {
        .ai_left_content {
          height: 50%;
          padding-left: 30px;
        }

        .ai_br_content {
          padding-left: 32px;
          max-width: 300px;
        }

        .ai_bl_content {
          padding-left: 32px;
          max-width: 300px;
        }

        .ai_top_content {
          width: 50%;
          padding-left: 32px;
          max-width: 300px;
        }
      }

      @media (max-width: 1367px) {
        .ai_color_box {
          min-height: 600px;
          box-sizing: border-box;
          padding-left: 0px !important;
        }

        .ai_left_content {
          height: 50%;
          padding-left: 62px;
        }

        .ai_br_content {
          padding-left: 35px;
          max-width: 330px;
        }

        .ai_bl_content {
          padding-left: 48px;
          max-width: 330px;
        }

        .ai_top_content {
          width: 50%;
          padding-left: 48px;
          max-width: 330px;
        }

        .ai_box_left {
          min-height: 600px;
        }

        .ai_box_right {
          min-height: 600px;
        }
      }

      @media (max-width: 768.98px) {
        #partAlbum {
          max-height: 546px;
          padding: 0px 12px;
        }

        .mask_btn_box {
          bottom: 0rem;
          /* margin: 0px 24px; */
        }

        .part-album {
          gap: 0px;
        }

        .ai_mobile {
          margin-top: 11.2px;
        }

        .ai_top {
          margin-bottom: 0px;
        }

        .ai_bl_content {
          padding: 2rem;
        }

        .ai_br_content {
          padding: 2rem;
        }

        .ai_box_right {
          min-height: 950px;
        }
      }

      /* 跑马灯边框的伪元素 */
      .glowing-border::before {
        content: "";
        position: absolute;
        top: -0.5rem;
        left: -0.5rem;
        right: -0.5rem;
        bottom: -0.5rem;
        border-radius: 2.3rem;
        /* 比父元素稍大 */
        background: conic-gradient(
          from 0deg at 50% 50%,
          /* 从0度开始 */ transparent 0%,
          /* 初始透明 */ #47f7df 10%,
          /* 高亮颜色 */ transparent 20% /* 恢复透明 */
        );
        background-size: 400% 400%;
        animation: border-run 3s linear infinite;
        /* 动画 */
        z-index: -1;
      }

      .partner_box {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-wrap: wrap;
      }

      .partner_box span {
        padding-right: 1.5rem;
        font-size: 19.2px;
      }

      .partner_box img {
        height: 5rem;
        padding-right: 4rem;
      }
    </style>
    <!-- faq css-->
    <style>
      .part-faq .faq-option {
        box-sizing: border-box;
        border-bottom: 1px solid rgba(255, 255, 255, 0.15);
        /* padding: 1rem 0 2rem; */
        padding: 24px 0px;
        padding-bottom: 32px;
      }

      .part-faq .faq-option:first-child {
        padding-top: 4px;
      }

      .part-faq .faq-option .faq-item {
        font-size: 22px;
        transition: all 0.3s;
      }

      .part-faq .faq-option .faq-item.collapsed {
        color: rgba(255, 255, 255, 0.6);
        line-height: 30px;
        padding: 12px 0px;
        font-size: 22px;
      }

      .part-faq .faq-option .faq-item.collapsed:hover {
        color: #fff;
      }

      .part-faq .faq-option .faq-item[aria-expanded="true"] {
        color: #fff;
      }

      .part-faq .faq-option .faq-item svg:nth-child(1) {
        display: none;
      }

      .part-faq .faq-option .faq-item svg:nth-child(2) {
        display: inline-block;
      }

      .part-faq .faq-option .faq-item.collapsed svg:nth-child(1) {
        display: inline-block;
      }

      .part-faq .faq-option .faq-item.collapsed svg:nth-child(2) {
        display: none;
      }

      .secondFaq {
        line-height: 24px;
        font-size: 16px;
        padding-top: 20px;
      }

      ol,
      ul,
      dl {
        margin-bottom: 0px;
      }

      @media (max-width: 576px) {
        .part-faq .faq-option {
          padding: 0;
        }

        .part-faq {
          padding-top: 48.8px;
        }

        .secondFaq {
          line-height: 22px;
          padding-top: 8px;
          font-size: 14px;
        }

        .secondFaq.pt-3 {
          padding-top: 16px !important;
        }

        .secondFaq.pb-4 {
          padding-bottom: 24px !important;
        }

        .part-faq .faq-option .faq-item {
          font-size: 14px;
          padding-top: 12px;
          font-weight: 700;
        }

        .part-faq .faq-option:first-child {
          padding-top: 0;
        }

        .part-faq .faq-option .faq-item.collapsed {
          color: rgba(255, 255, 255, 0.6);
          line-height: 22px;
          padding: 24px 0px;
          font-size: 14px;
          font-weight: 700;
        }
      }
    </style>
    <!-- 邀请码 -->
    <style>
      .yqm_btn {
        margin-top: 2.1875rem;
        line-height: 20px;
      }

      .invite_code_content {
        background: rgba(31, 31, 31, 1);
      }

      .code_pic {
        min-height: 267px;
        background: url("https://www.tomoviee.ai/images/home/<USER>") no-repeat;
        background-size: 100%;
        background-position: center top;
      }

      .code_title {
        font-size: 28px;
        font-weight: 700;
        line-height: 30px;
      }

      .code_title_sub {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.56);
        font-weight: 700;
      }

      /* .code_ws_box {
      position: relative;
    } */

      .code_ws {
        color: rgba(134, 128, 255, 1);
        font-size: 1rem;
        font-weight: 400;
        text-decoration: underline;
        display: inline-block;
        position: relative;
      }

      .code_ws::after {
        content: "";
        /* 必须设置 content */
        position: absolute;
        display: none;
        top: -10px;
        /* 向下延伸 10px */
        left: 50%;
        /* 居中 */
        transform: translateX(-50%);
        /* 调整居中位置 */
        /* 绘制三角形 */
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        /* 左边透明 */
        border-right: 10px solid transparent;
        /* 右边透明 */
        border-top: 10px solid rgba(61, 61, 61, 1);
        /* 底部边框形成三角形 */
      }

      .qrcode_ws {
        position: absolute;
        display: none;
        top: -250px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
        line-height: 16px;
        padding: 12px;
        background-color: rgba(61, 61, 61, 1);
        border-radius: 1rem;
        color: #ffffff99;
        z-index: 5;
      }

      .qrcode_ws img {
        border-radius: 1rem;
        width: 200px;
      }

      .code_ws:hover::after {
        display: block;
      }

      .code_ws:hover .qrcode_ws {
        display: block;
      }

      .code_form {
        padding: 0px 2rem;
        position: relative;
      }

      .int_btnbox {
        position: absolute;
        right: 42px;
        top: 10px;
        z-index: 3;
      }

      .inv-btn {
        height: 48px;
        padding: 0px 0px;
        width: 125px;
        line-height: 46px;
      }

      .invalid-feedback {
        text-align: left;
      }

      @media (max-width: 768px) {
        .code_pic {
          min-height: 139px;
        }

        .code_title {
          font-size: 18px;
          font-weight: 700;
          line-height: 22px;
        }

        .yqm_btn {
          margin-top: 2.11rem;
        }

        .modal-content {
          margin: 0px 15px;
        }

        .int_btnbox {
          right: 38px;
        }

        .qrcode_ws {
          top: -216px;
        }

        .qrcode_ws img {
          border-radius: 1rem;
          width: 150px;
        }

        /* .qrcode_ws p {
        padding: 4px 8px;
      } */

        .inv-btn {
          height: 46px;
          line-height: 44px;
        }
      }
    </style>
    <!-- 表单样式 -->
    <style>
      .was-validated .form-control:invalid,
      .form-control.is-invalid {
        border-color: #ffffff29;
        background: #1f1f1f;
        padding-right: calc(1.5em + 1.4375rem);
        background-image: none;
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.35938rem) center;
        background-size: calc(0.75em + 0.71875rem) calc(0.75em + 0.71875rem);
        color: #fff;
      }

      .form-control {
        box-shadow: none;
        border-radius: 0.75rem;
        border: 1px solid #ffffff29;
        height: 60px;
        font-size: 1rem;
      }

      .form-control.is-invalid:focus {
        border-color: #723fff;
        background: #1f1f1f;
        padding-right: calc(1.5em + 1.4375rem);
        background-image: none;
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.35938rem) center;
        background-size: calc(0.75em + 0.71875rem) calc(0.75em + 0.71875rem);
        box-shadow: none;
      }
    </style>
    <style>
      .section-mobile-10 {
        background-image: url("https://www.tomoviee.ai/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: 100%;
        background-position: bottom center;
      }

      .section-10 {
        background-image: url("data:image/svg+xml,%3Csvg width='1920' height='1315' viewBox='0 0 1920 1315' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg opacity='0.18' clip-path='url(%23clip0_4_414)' filter='url(%23filter0_f_4_414)'%3E%3Cpath d='M-171 256L-171 1059L2202 1059L2202 256L-171 256Z' fill='black'/%3E%3Cpath d='M-171 256L-171 1059L2202 1059L2202 256L-171 256Z' fill='%23907CFF' fill-opacity='0.387813'/%3E%3Cmask id='mask0_4_414' style='mask-type:luminance' maskUnits='userSpaceOnUse' x='-171' y='256' width='2373' height='803'%3E%3Cpath d='M-171 256L-171 1059L2202 1059L2202 256L-171 256Z' fill='white'/%3E%3C/mask%3E%3Cg mask='url(%23mask0_4_414)'%3E%3Cg filter='url(%23filter1_f_4_414)'%3E%3Cpath d='M227.961 380.465L227.961 1007.81L1700.7 1007.81L1700.7 380.465L227.961 380.465Z' fill='%23110042'/%3E%3Cpath d='M-89.4281 291.633L-89.4281 850.722L1178.64 850.722L1178.64 291.633L-89.4281 291.633Z' fill='%23620071'/%3E%3Cpath d='M971.006 341.319L971.006 781.463L2207.93 781.463L2207.93 341.319L971.006 341.319Z' fill='%23550097'/%3E%3C/g%3E%3C/g%3E%3Cg style='mix-blend-mode:overlay'%3E%3Cpath d='M-171 256L-171 1059L2202 1059L2202 256L-171 256Z' fill='%23808080'/%3E%3C/g%3E%3C/g%3E%3Cdefs%3E%3Cfilter id='filter0_f_4_414' x='-427' y='0' width='2885' height='1315' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='BackgroundImageFix' result='shape'/%3E%3CfeGaussianBlur stdDeviation='128' result='effect1_foregroundBlur_4_414'/%3E%3C/filter%3E%3Cfilter id='filter1_f_4_414' x='-372.241' y='8.82031' width='2862.99' height='1281.8' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='BackgroundImageFix' result='shape'/%3E%3CfeGaussianBlur stdDeviation='141.406' result='effect1_foregroundBlur_4_414'/%3E%3C/filter%3E%3CclipPath id='clip0_4_414'%3E%3Crect width='803' height='2373' fill='white' transform='translate(-171 1059) rotate(-90)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
        background-repeat: no-repeat;
        background-size: 100% 100%;
        background-position: bottom center;
      }

      .vv-container {
        position: relative;
        overflow: hidden;
        background-size: cover;
        transform: scale(1);
        width: 940px;
        max-width: 100%;
      }

      .vv-container::before {
        content: "";
        display: block;
        padding-top: calc(560 / 940 * 100%);
      }

      .vv-container video {
        position: absolute;
        width: 100%;
        height: auto;
        object-fit: cover;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        overflow: inherit;
        border-radius: 24px;
      }

      .video-box-right {
        position: relative;
        overflow: hidden;
        background-size: cover;
        transform: scale(1);
        width: 720px;
        max-width: 100%;
        /* margin: 0 auto; */
      }

      .video-box-right::before {
        content: "";
        display: block;
        padding-top: calc(540 / 720 * 100%);
      }

      .video-box-right video {
        position: absolute;
        width: 100%;
        height: auto;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        overflow: inherit;
        /* border-radius: 24px; */
      }

      @media (max-width: 768px) {
        .video-box-right video {
          width: 100%;
        }

        .swiperTab-pagination .swiper-pagination-bullet-active,
        .video-swiper-pagination .swiper-pagination-bullet-active {
          background-image: linear-gradient(180deg, #fff, #806fff);
        }

        .font-size-small {
          font-size: 12px;
        }

        .ws-video video {
          width: 100%;
          border-radius: 12px;
        }

        .audio-no-mobile {
          bottom: 58%;
          right: 12px;
          position: absolute;
          width: 3rem;
          height: 3rem;
          z-index: 10;
          background-image: url("data:image/svg+xml,%3Csvg width='48' height='48' viewBox='0 0 48 48' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0 24C0 10.7452 10.7452 0 24 0C37.2548 0 48 10.7452 48 24C48 37.2548 37.2548 48 24 48C10.7452 48 0 37.2548 0 24Z' fill='black' fill-opacity='0.28'/%3E%3Cg clip-path='url(%23clip0_623_4012)'%3E%3Cpath d='M15.8502 19.3249H11.9502C11.1252 19.3249 10.4502 19.9999 10.4502 20.8249V26.2249C10.4502 27.0499 11.1252 27.7249 11.9502 27.7249H15.7752L23.3502 34.5499V32.2249V14.8249V12.6499L21.7002 14.0749L15.8502 19.3249Z' fill='white'/%3E%3Cpath d='M37.1498 19.8497L35.5748 18.1997L31.8248 21.9497L28.0748 18.1997L26.4248 19.8497L30.1748 23.5997L26.4998 27.2747L27.9998 28.9247L31.7498 25.1747L35.4998 28.9247L36.9998 27.2747L33.3998 23.5997L37.1498 19.8497Z' fill='white'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_623_4012'%3E%3Crect width='26.7' height='21.9' fill='white' transform='translate(10.4502 12.6499)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E%0A");
          background-size: 3rem 3rem;
          cursor: pointer;
        }
      }
    </style>
    <style>
      .bt-video-container {
        position: relative;
        width: 100%;
        height: auto;
        height: 800px;
      }

      .bt-video-container video {
        height: 800px;
        margin: auto;
      }

      .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 3;
      }

      .animated-content {
        opacity: 0;
        transform: scale(0.1);
        transition: opacity 1s ease, transform 1s ease;
      }

      .animated-content.show {
        opacity: 1;
        transform: scale(1);
      }

      @media (max-width: 768px) {
        .video-overlay {
          display: none;
        }

        iframe[id^="_QD_DISPLAY_IFRAME_SOCKET_IFRAME"] {
          display: none;
        }
      }
    </style>
    <!-- 竖屏滚动 -->
    <style>
      .album-img {
        position: relative;
        display: inline-block;
        /* 或者根据你的布局需要 */
      }

      .album-btn {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 2;
      }

      .album-img:hover .album-btn {
        opacity: 0.6;
      }

      .album-img::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        /* background: rgba(255, 255, 255, 0.34); */
        background: linear-gradient(0deg, rgba(0, 0, 0, 0.34) 0%, rgba(0, 0, 0, 0.34) 100%);
        backdrop-filter: blur(5px);
        z-index: 1;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .album-img:hover::before {
        opacity: 1;
      }

      .album-img img {
        display: block;
        transition: filter 0.3s ease;
      }

      .album-img:hover img {
        filter: brightness(0.7);
      }

      @media (max-width: 768px) {
        .album-img:hover .album-btn {
          opacity: 0;
        }

        .album-img:hover::before {
          opacity: 0;
        }
      }

      .btn-md {
        display: flex;
        width: 248px;
        height: 48px;
        min-width: 112px;
        border-radius: 10px;
        background: rgba(255, 255, 255, 0.1);
        border: none;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-size: 14px;
      }

      .btn-md:hover {
        color: #fff;
      }

      .status_text {
        color: rgba(255, 255, 255, 0.8);
        text-align: center;

        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 32px;
        padding-bottom: 32px;
      }

      .status_text_sub {
        color: rgba(255, 255, 255, 0.6);
        display: none;
      }

      .h5Title {
        color: rgba(255, 255, 255, 0.8);
        font-size: 20px;
      }
    </style>
    <!-- swiper css -->
    <style>
      .swiper .swiper-pagination-bullet {
        background-color: #999;
        opacity: 0.3;
      }

      .swiper .swiper-pagination-bullet-active {
        opacity: 1;
      }

      .swiper .swiper-slide {
        height: auto;
      }

      .edit-swiper .video-bg {
        overflow: hidden;
      }
    </style>
    <!-- 移动端字体大小兼容 -->
    <style>
      @media (max-width: 1279px) {
        .section-5 {
          margin-top: 120px;
        }

        .section-8 {
          height: 985px;
        }

        .subdisc {
          max-width: 100%;
        }

        .mobile-section-4 {
          margin-top: 120px;
        }

        .video-box-right {
          width: 0px !important;
          width: 100% !important;
        }
      }

      @media (max-width: 768px) {
        .h1title {
          font-size: 36px;
          line-height: 44px;
          font-weight: 700;
          padding-top: 16px;
          padding-bottom: 8px;
          max-width: 320px;
          margin: auto;
        }

        .status_text {
          color: #fff;
          text-align: center;

          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          padding-bottom: 24px;
          max-width: 270px;
          margin: auto;
        }

        .mobile-section-4 {
          margin-top: 61.6px;
        }

        .display-3 {
          font-size: 24px;
        }

        .h2 {
          font-size: 1.40625rem;
        }

        .h5 {
          font-size: 12px;
          max-width: 251px;
          margin: auto;
        }

        .mobile_subtitle {
          font-size: 16px;
          line-height: 24px;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.6);
          /* max-width: 300px; */
          margin: auto;
        }

        .yqm_btn .h5 {
          font-size: 18px;
        }

        .h5Title {
          color: rgba(255, 255, 255);
          line-height: 26px;
          font-size: 18px;
        }

        .subdisc {
          font-size: 14px;
          max-width: 308px;
          margin: auto;
        }

        .rounded-8 {
          border-radius: 4px;
        }

        .subtitle.mb-2 {
          margin-bottom: 8px !important;
        }

        .subtitle {
          line-height: 36px;
        }

        .line-height-3rem {
          line-height: 26px;
        }

        .h2 {
          margin-bottom: 4px;
        }

        .mb-3 {
          margin-bottom: 12px !important;
        }

        .font-size-large {
          font-size: 14px !important;
        }

        .line-height-25 {
          line-height: 24px;
        }

        .section-2 {
          padding-top: 64px;
        }

        .border-radus-24 {
          border-radius: 12px;
        }

        .mb-16 {
          margin-bottom: 16px;
        }

        .h5sub {
          font-size: 16px;
          max-width: 100%;
        }

        .line-height-2rem {
          line-height: 22px;
        }

        .line-height-28 {
          line-height: 22px;
        }

        .section-2 .vv-container video {
          border-radius: 12px;
          width: 100%;
        }

        .limit_width {
          max-width: 288px;
          margin: auto;
        }

        .limit_width2 {
          max-width: 306px;
          margin: auto;
        }

        .section-8 {
          background: none;
          height: auto;
        }

        .section-5 {
          margin-top: 23.2px;
        }

        .for_bg {
          background: none;
        }

        .partner_box {
          display: block;
          text-align: center;
        }

        .partner_box span {
          padding-right: 0rem;
          font-size: 18px;
        }

        .partner_box .hwbox {
          display: flex;
          justify-content: center;
          gap: 16px;
          padding-top: 16px;
        }

        .partner_box .hwbox img {
          height: 48px;
          padding-right: 0px;
        }

        .faqsub {
          margin-bottom: 40px;
        }

        .part-faq .faq-option {
          border-bottom: 0.5px solid rgba(255, 255, 255, 0.2);
        }

        .part-faq .faq-option:first-child {
          border-top: 0.5px solid rgba(255, 255, 255, 0.2);
        }

        .album-box1,
        .album-box2,
        .album-box3,
        .album-box4,
        .album-box5 {
          display: flex;
          flex-direction: column;
          width: 32%;
          height: 100%;
          padding: 0px 4px;
        }

        .btn-md {
          width: 70px;
          font-size: 12px;
          min-width: 70px;
          padding: 0.5rem 0px;
        }

        .album-title {
          font-size: 12px;
          transform: scale(0.8);
        }

        .part-album .album-img {
          width: auto;
          opacity: 0.8;
          margin: 0px;
          padding: 4px 0px;
          transition: all 1s;
          position: relative;
          /* border-radius: 4px; */
        }

        .album-img img {
          border-radius: 4px;
        }
      }
      @media (max-width: 576px) {
        .modal-dialog {
          max-width: 100% !important;
        }
      }
    </style>
    <style>
      #canvas {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%) scale(1);
        width: 100%;
        text-align: center;
        z-index: 2;
        transform-origin: center center;
      }
    </style>
    <!-- 新版滚动 -->

    <style>
      .part-advantages .advantages-tag {
        width: fit-content;
        padding: 8px 28px;
        border: 1px solid #656565;
        background: linear-gradient(270deg, rgba(255, 255, 255, 0.104) 0, rgba(255, 255, 255, 0.208) 100%);
      }

      .part-advantages .advantagesSwiper .swiper-slide {
        opacity: 1 !important;
        transition: 1s cubic-bezier(0.05, 0.61, 0.41, 0.95) !important;
      }

      .part-advantages .advantagesSwiper .swiper-slide .video-box-right::after {
        content: "";
        position: absolute;
        top: 0px;
        width: 100%;
        height: 100%;
        z-index: 1;
        background-color: rgba(0, 0, 0, 0.5);
      }

      .part-advantages .advantagesSwiper .swiper-slide-active {
        opacity: 1 !important;
      }

      .part-advantages .advantagesSwiper .swiper-slide-active .video-box-right::after {
        content: "";
        position: absolute;
        top: 0px;
        width: 0;
        height: 0;
        z-index: 0;
        background-color: rgba(0, 0, 0, 0.6);
      }

      .part-advantages .advantages-wrap {
        position: relative;
        top: 0px;
      }

      @media (min-width: 1280px) {
        .part-advantages .progress-contanier,
        .part-advantages .sticky-wrapper {
          position: sticky;
          position: -webkit-sticky;
          top: 0;
          height: 100vh;
        }

        .part-advantages .progress-contanier {
          width: 24px;
        }

        .part-advantages .advantagesSwiper {
          height: 560px;
        }

        .part-advantages .advantagesSwiper.firstMove {
          transition: transform 0.3s linear;
        }

        .part-advantages .advantagesSwiper-progress {
          position: absolute;
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          font-size: 1.125rem;
          font-weight: 700;
          top: 50%;
          left: 0;
          transform: translateY(-50%);
        }

        .part-advantages .swiper-progress-bar {
          height: 500px;
          margin: 10px 0;
          background-color: rgba(255, 255, 255, 0.3);
          width: 2px;
          position: relative;
          --progress: 33.33%;
        }

        .part-advantages .swiper-progress-bar::before {
          position: absolute;
          content: "";
          width: 4px;
          height: var(--progress);
          background-color: #50e3c2;
          border-radius: 10px;
          transition: height 0.3s linear;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
        }

        .part-advantages .advantages-list {
          /* max-width: 490px */
        }

        .part-advantages .advantages-list .list-item {
          opacity: 0.5;
          min-height: 100vh;
          display: flex;
          justify-content: center;
          flex-direction: column;
        }

        .part-advantages .advantages-list .list-item.active {
          opacity: 1;
        }
      }

      @media (max-width: 1600px) {
        .advantagesSwiper-pagination.swiper-horizontal > .swiper-pagination-bullets,
        .advantagesSwiper-pagination.swiper-pagination-bullets.swiper-pagination-horizontal,
        .advantagesSwiper-pagination {
          width: 15px;
          bottom: 50%;
          transform: translateY(50%);
          right: -25px;
          left: auto;
        }
      }

      @media (min-width: 2000px) {
        .part-banner .banner-video {
          border-radius: 10px 10px 0 0;
        }

        .part-assets {
          background-size: 707px auto;
        }
      }

      @media (max-width: 1440px) {
        .part-advantages .advantages-wrap {
          position: relative;
          top: 10vh;
        }

        .part-advantages .progress-contanier,
        .part-advantages .sticky-wrapper {
          height: 124vh;
        }

        .section-4-new {
          margin-top: -210px;
        }

        .part-advantages .advantages-list .list-item {
          height: 140vh;
        }

        .part-advantages .advantagesSwiper {
          height: 720px;
        }

        .part-advantages .font-size-42 {
          font-size: 1.75rem;
        }

        .part-advantages .swiper-progress-bar {
          height: 350px;
        }

        .advantagesSwiper-pagination.swiper-horizontal > .swiper-pagination-bullets,
        .advantagesSwiper-pagination.swiper-pagination-bullets.swiper-pagination-horizontal,
        .advantagesSwiper-pagination {
          width: 15px;
          bottom: 56%;
          transform: translateY(50%);
          right: -25px;
          left: auto;
        }
      }

      @media (max-width: 1280px) {
        .section-4-new {
          display: none !important;
        }

        .mobile-section-4 {
          display: block !important;
        }

        .part-advantages .advantages-list {
          width: 100%;
        }

        .part-advantages .advantages-list .list-item {
          height: auto;
        }

        .part-advantages .advantages-wrap {
          top: 0;
        }

        .part-advantages .advantages-list .list-item > div {
          padding: 1.5rem;
        }

        .part-advantages .advantages-list .list-item {
          display: flex;
          align-items: center;
          margin: 1rem 0;
        }

        .part-advantages .advantages-list .list-item .video-box {
          width: 550px;
          max-width: 100%;
          position: relative;
        }
      }

      @media (max-width: 992px) {
        .part-advantages .advantages-tag {
          padding: 0 16px;
        }
      }

      @media (max-width: 576px) {
        .part-advantages .font-size-42 {
          font-size: 24px;
          margin-top: 16px;
        }

        .part-advantages .advantages-list .list-item {
          flex-direction: column;
          margin-bottom: 64px;
        }

        .part-advantages .advantages-list .list-item > div {
          padding: 0 9px;
          width: 100%;
        }

        .part-advantages .advantages-tag {
          display: none;
        }

        .part-advantages .tag-video {
          position: absolute;
          top: 0;
          left: 0;
          border: 1px solid #636363;
          border-top: 0;
          border-left: 0;
          background: #2c2c2c;
          text-align: center;
          color: #fff;
          font-size: 16px;
          font-weight: 700;
          padding: 0px 12px;
          border-radius: 0 0 6px 0;
          font-family: "Outfit";
        }

        .part-advantages .desc-list {
          display: flex;
          overflow-x: scroll;
          margin: 8px -24px 16px;
          padding: 0 24px;
          scrollbar-width: none;
          -ms-overflow-style: none;
        }

        .part-advantages .desc-list .desc-box {
          background-color: #1e2125;
          border-radius: 8px;
          padding: 10px;
          font-size: 14px;
          text-align: left;
          margin-right: 10px;
          width: 280px;
          color: rgba(255, 255, 255, 0.7);
          flex-shrink: 0;
        }

        .part-advantages .desc-list .desc-box ~ div {
          color: #fff;
          margin-bottom: 4px;
        }

        .part-advantages .box-btn {
          height: 48px;
          border-radius: 10px;
          font-size: 16px;
          min-height: auto !important;
          width: 100%;
        }
      }

      @keyframes move {
        0% {
          transform: translateX(0);
        }

        50% {
          transform: translateX(6px);
        }

        100% {
          transform: translateX(0);
        }
      }
    </style>

    <style>
      .album-title {
        display: none;
      }
    </style>
    <script type="text/javascript">
      var CHANNEL_ID = "1372";
      var SITE_ID = "1086";
      var CMS_LANGUAGE = "en";
      var TEMPLATE_ID = "10014989";
      var PAGE_ID = "538184";
      var TEMPLATE_MODULE = "other";
      var TEMPLATE_TYPE = "other";
    </script>
  </head>
  <body data-pro="tomoviee" data-sys="auto" data-dev="auto">
    <!-- Google Tag Manager (noscript) -->
    <noscript
      ><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-WPNBJKV" height="0" width="0" style="display: none; visibility: hidden"></iframe
    ></noscript>
    <!-- End Google Tag Manager (noscript) -->
    <!--天幕2.0头部-->
    <link rel="stylesheet" href="https://neveragain.allstatics.com/2019/assets/style/font-custom-ws.min.css" media="all" />
    <link rel="stylesheet" href="https://neveragain.allstatics.com/2019/assets/style2021/header-footer.min.css" />
    <!-- 头部公共样式块 -->
    <style>
      .wsc-footer2020 .wsc-footer2020-top-content {
        margin: 0 -16px;
        padding: 0;
      }

      .wsc-footer2020 .wsc-footer2020-nav {
        text-align: center;
      }

      .wsc-footer2020 .wsc-footer2020-subnav {
        border-bottom: 0;
        padding: 0;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-toggle {
        padding: 16px;
      }

      .wsc-footer2020 .wsc-footer2020-subnav .wsc-footer2020-dropdown {
        border-bottom: solid 1px rgba(255, 255, 255, 0.2);
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-item {
        border-bottom: solid 1px rgba(0, 0, 0, 0.2);
        padding: 16px;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-link {
        color: #000;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-link .wsc-footer2020-subnav-img-mobile {
        display: block;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-link .wsc-footer2020-subnav-img {
        display: none;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-menu {
        background-color: #fff;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-menu ul {
        padding: 0;
      }

      .wsc-footer2020-bottom .wsc-footer2020-container {
        font-size: 12px;
      }

      .wsc-footer2020-bottom .wsc-footer2020-container,
      .wsc-footer2020 .wsc-footer2020-bottom .wsc-footer2020-copyright a {
        color: rgba(255, 255, 255, 0.7);
      }

      .wsc-footer2020 .wsc-footer2020-bottom .wsc-footer2020-copyright .wsc-footer2020-copyright-bottom span {
        display: inline-block;
        padding: 0 0.5rem;
      }

      .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-brand-logo {
        display: inline-block;
        padding: 0 !important;
      }

      .wsc-footer2020 .wsc-footer2020-social {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px;
        color: #fff;
        border-bottom: solid 1px rgba(255, 255, 255, 0.2);
      }

      .wsc-footer2020 .wsc-footer2020-social .wsc-footer2020-social-item {
        display: flex;
        align-items: center;
      }

      .wsc-footer2020 .wsc-footer2020-social .wsc-footer2020-subnav-iconlink {
        padding: 0 0 0 2rem;
      }

      .wsc-footer2020 .wsc-footer2020-dropdown-toggle .wsc-footer2020-dropdown-title {
        color: #fff;
      }

      @media (min-width: 1280px) {
        .wsc-footer2020 .wsc-footer2020-top-content {
          position: relative;
          margin: 0;
        }

        .wsc-footer2020 .wsc-footer2020-subnav {
          padding: 8px 0;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-toggle {
          padding: 8px 0;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-menu {
          background-color: initial;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-item {
          padding: 8px 0;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-link {
          color: #fff;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-link .wsc-footer2020-subnav-img {
          display: block;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-link .wsc-footer2020-subnav-img-mobile {
          display: none;
        }

        .wsc-footer2020 .wsc-footer2020-subnav .wsc-footer2020-dropdown {
          border-bottom: 0;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-social {
          position: absolute;
          z-index: 2;
          top: 100%;
          right: 0;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-nav {
          order: 1;
          padding: 4rem 0;
          text-align: left;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-subnav {
          order: 2;
          padding: 4rem;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content {
          justify-content: space-around;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-toggle .wsc-footer2020-dropdown-title {
          font-size: 22px;
        }

        .wsc-footer2020 .wsc-footer2020-social {
          padding: 1.5rem 0;
          border-bottom: 0;
          color: rgba(255, 255, 255, 0.7);
          justify-content: flex-start;
        }

        .wsc-footer2020 .wsc-footer2020-social .wsc-footer2020-social-title {
          font-size: 16px;
          margin: 0;
          font-weight: 400;
          line-height: 1.5;
        }

        .wsc-footer2020 .wsc-footer2020-bottom {
          padding-bottom: 0;
        }

        .wsc-footer2020 .wsc-footer2020-bottom .wsc-footer2020-container {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .wsc-footer2020 .wsc-footer2020-bottom .wsc-footer2020-container .wsc-footer2020-copyright {
          order: 1;
        }

        .wsc-footer2020 .wsc-footer2020-bottom .wsc-footer2020-container .wsc-footer2020-dropdown {
          order: 2;
        }

        .wsc-footer2020 .wsc-footer2020-bottom .wsc-footer2020-copyright-link {
          padding: 0;
        }

        .wsc-footer2020 .wsc-footer2020-dropdown-toggle .wsc-footer2020-dropdown-icon {
          display: none;
        }

        .wsc-footer2020-subnav-item {
          background-color: #000;
        }
      }
    </style>
    <!-- 头文件样式 -->
    <style>
      body,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      .h1,
      .h2,
      .h3,
      .h4,
      .h5,
      .h6 {
        font-family: "MessinaSansWeb", -apple-system, blinkmacsystemfont, "Segoe UI", roboto, arial, "Noto Sans", sans-serif, "Apple Color Emoji",
          "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      }

      body {
        background-color: #000;
        color: #fff;
      }

      .btn {
        text-transform: none;
      }
      .btn:focus,
      .btn.focus {
        box-shadow: none !important;
      }

      .btn-action {
        border-color: #723fff !important;
        background-color: #723fff !important;
      }

      @media (max-width: 575.98px) {
        .container {
          padding-left: 24px;
          padding-right: 24px;
        }
        .modal-dialog {
          max-width: 100% !important;
        }
      }

      .wsc-header2020 {
        position: sticky;
        top: 0;
        width: 100%;
        z-index: 100;
      }

      .wsc-footer2020 {
        overflow: hidden;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-collapse {
        background-color: transparent;
      }

      .wsc-header2020 .wsc-header2020-navbar-master {
        background: transparent;
        /* backdrop-filter: blur(25px); */
      }

      .wsc-header2020 .wsc-header2020-navbar-master::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        /* filter: blur(300px); */
      }

      .wsc-header2020 .wsc-header2020-navbar-collapse-toggle[aria-expanded="true"]:before {
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(25px);
        -webkit-backdrop-filter: blur(25px);
        z-index: -1;
      }

      .wsc-header2020-navbar-item.wondershare-user-panel {
        padding: 24px 0px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-collapse {
        background: transparent;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-brand .div_a span {
        padding-left: 8px;
        color: #ffffff;
        margin-top: 10px;
        margin-left: 136px;
        display: inline-block;
        border-left: 1px solid #ffffff;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle,
      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item .wsc-header2020-navbar-link {
        font-size: 16px;
        font-weight: 400;
        opacity: 1;
        color: #fff;
        position: relative;
      }

      /* .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle:hover,
    .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item .wsc-header2020-navbar-link:hover {
      background: rgba(0, 0, 0, 0.30);
    }


    .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item:hover .wsc-header2020-navbar-link:hover::after {
      border-bottom: 0px solid #fff;
      background: rgba(224, 74, 74, 0.3);
    } */

      .wsc-header2020 .wsc-header202004-navbar-wondershare .wsc-header2020-navbar-link {
        height: 80px;
        line-height: 80px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown,
      .wsc-header2020 .wsc-header2020-navbar-item {
        position: relative;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown::after {
        content: "";
        width: calc(100% - 48px);
        height: 1px;
        background: rgba(255, 255, 255, 0.1);
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item:not(.log-out)::after {
        content: "";
        width: calc(100% - 48px);
        height: 1px;
        background: rgba(255, 255, 255, 0.1);
        position: absolute;
        /* bottom: -1px; */
        left: 50%;
        transform: translateX(-50%);
      }

      .log-in,
      .log-out {
        padding-top: 0px !important;
      }

      .header_btn_panel::after {
        height: 0px !important;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-nav:first-child {
        display: none;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-box {
        box-shadow: inset 0 0 0 rgba(0, 0, 0, 0.2);
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-main .wsc-header2020-navbar-brand {
        padding: 10px 0;
      }

      .wsc-header2020 .wsc-header202004-navbar-wondershare .wsc-header2020-navbar-brand {
        height: 48px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-menu {
        background-color: #ffffff;
        line-height: 1;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-list {
        display: block;
      }

      .wsc-header2020 .wsc-header2020-dropdownMenuBody-list {
        padding-left: 0px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-title,
      .wsc-header2020 .wsc-header2020-dropdownMenuBody-list li {
        line-height: 1;
        font-size: 14px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-title > a:hover {
        font-weight: 400;
        text-decoration: none;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-brand a {
        background-image: none;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-brand .div_a {
        width: 214px;
        background-image: url(https://neveragain.allstatics.com/2019/assets/icon/logo/tomovieeai-group-white.svg);
        background-repeat: no-repeat;
        position: relative;
        z-index: 20;
        height: 40px;
        background-size: contain;
      }

      .icon-weixin {
        position: relative;
      }

      .icon-weixin:hover .icon-weixin-box {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
      }

      .icon-weixin .icon-weixin-box {
        position: absolute;
        bottom: 34px;
        left: 50%;
        display: none;
        width: 100%;
        min-width: 110px;
        background-color: #ffffff;
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        transform: translateX(-50%);
        z-index: 3;
        font-style: normal;
        border-radius: 8px;
      }

      .icon-weixin .icon-weixin-box .icon-weixin-box-text {
        line-height: 1;
        font-size: 12px;
        padding: 6px 0px;
      }

      .icon-weixin .icon-weixin-box img {
        max-width: 100%;
        border-radius: 4px;
        border: 0.5px solid rgba(0, 0, 0, 0.12);
      }

      .icon-weixin .icon-weixin-box:after {
        position: absolute;
        display: block;
        content: "";
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        /* 左边透明边框 */
        border-right: 6px solid transparent;
        /* 右边透明边框 */
        border-top: 6px solid white;
        /* 上边白色边框 - 形成向下三角形 */
        /* 调整位置，根据实际需要修改 */
        bottom: -6px;
        left: 50%;
        transform: translateX(-50%);
      }

      .icon-weixin .icon-weixin-box[position-absolute="right"] {
        right: 0;
        left: auto;
        -webkit-transform: translateX(0);
        -moz-transform: translateX(0);
        transform: translateX(0);
      }

      .icon-weixin .icon-weixin-box[position-absolute="left"] {
        left: 0;
        -webkit-transform: translateX(0);
        -moz-transform: translateX(0);
        transform: translateX(0);
      }

      .icon-get-app {
        position: absolute;
        bottom: 52px;
        left: 50%;
        display: none;
        /* width: 100%; */
        min-width: 110px;
        background-color: #ffffff;
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        transform: translateX(-50%);
        z-index: 3;
        font-style: normal;
        border-radius: 8px;
      }

      .icon-get-app:after {
        position: absolute;
        display: block;
        content: "";
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        /* 左边透明边框 */
        border-right: 6px solid transparent;
        /* 右边透明边框 */
        border-top: 6px solid white;
        /* 上边白色边框 - 形成向下三角形 */
        /* 调整位置，根据实际需要修改 */
        bottom: -6px;
        left: 50%;
        transform: translateX(-50%);
      }

      .icon-get-app img {
        max-width: 100%;
        border-radius: 4px;
        border: 0.5px solid rgba(0, 0, 0, 0.12);
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-collapse-toggle[aria-expanded="true"]::before {
        top: 0;
      }

      .wsc-header2020-dropdownMenuBody-list li span {
        color: rgba(255, 255, 255, 0.6);
      }

      .wsc-header2020-dropdownMenuBody-list li:hover span {
        color: rgba(255, 255, 255, 1);
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-linkBtn {
        border: 2px solid rgba(255, 255, 255, 0.5);
        font-size: 14px;
        font-weight: 600;
        border-radius: 8px;
        background-color: transparent;
        color: rgba(255, 255, 255, 0.5);
        padding: 7.5px 24px;
        /* width: 120px; */
        text-transform: none;
        /* height: 48px;
      line-height: 46px;
      padding: 0px; */
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-linkBtn:hover {
        border: 2px solid rgba(255, 255, 255);
        color: #ffffff;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-box {
        padding: 10px 24px;
        margin-bottom: -16px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .dropdownMenuBodyFirst {
        padding-top: 0px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .dropdownMenuBodyFirst .wsc-header2020-dropdownMenuBody-title {
        padding-top: 0px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .dropdownMenuBodyTwo {
        padding-top: 0px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .dropdownMenuBodyTwo .wsc-header2020-dropdownMenuBody-title {
        padding-top: 0px;
      }

      .wsc-header2020 .wsc-header2020-navbar-nav {
        color: #fff;
        /* background: rgba(0, 0, 0, 0.50);
      backdrop-filter: blur(25px); */
      }

      .wsc-header2020 .wsc-header2020-navbar-nav > li {
        color: #fff;

        /* background: rgba(0, 0, 0, 0.50);
      backdrop-filter: blur(25px); */
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-item:last-child .wsc-header2020-dropdownMenuBody-box {
        margin-bottom: 0;
      }

      .wsc-header2020 .wsc-header2020-navbarDropdown-toggle[aria-expanded="true"]:before {
        height: 0vh;
      }

      .wsc-header2020 .wsc-header2020-navbarDropdown-toggle[aria-expanded="true"] + .wsc-header2020-navbarDropdown-menu {
        transition: none;
        /* background: transparent; */
        /* backdrop-filter: blur(0px); */
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(25px);
        box-shadow: none;
        /* padding: 0px 32px; */
        padding-top: 80px;
      }

      .wsc-header2020 .wsc-header2020-navbarDropdown-menu .wsc-header2020-container {
        padding-left: 25px;
        padding-right: 16px;
      }

      .wondershare-user-panel .avatar {
        margin: 0 0 0 24px;
        margin-right: 28px;
      }

      .wsc-header2020-dropdownMenuBody-box {
        border-right: 1px solid rgba(255, 255, 255, 0.1);
      }

      .wondershare-user-panel .ws-user-panel-dropdown {
        max-width: 12.5rem;
      }

      @media (min-width: 992px) {
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenu-body {
          padding-top: 18px;
          padding-bottom: 40px;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle svg {
          margin-left: 8px;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenu-body .wsc-header2020-dropdownMenuBody-content {
          display: flex;
          justify-content: start;
          /* margin-left: -60px; */
          /* margin-left: 110px; */
        }

        /* .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenu-body .wsc-header2020-dropdownMenuBody-content .wsc-header2020-dropdownMenuBody-item {
        flex: 1;
      } */

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-list {
          padding: 0;
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-product
          .wsc-header2020-navbarDropdown-menu
          > .wsc-header2020-container:last-child {
          border-top: 0px solid #e2e2e2;
          max-width: none;
          position: relative;
          /* background: rgba(0, 0, 0, 0.50);
        filter: blur(1px); */
        }

        /* .wsc-header2020 .wsc-header2020-navbarDropdown-toggle[aria-expanded="true"]+.wsc-header2020-navbarDropdown-menu::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(25px);
        
      } */

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-product
          .wsc-header2020-dropdownMenu-body
          .wsc-header2020-dropdownMenuBody-item {
          padding-right: 32px;
          padding-left: 0px;
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-business
          .wsc-header2020-dropdownMenu-body
          .wsc-header2020-dropdownMenuBody-item {
          padding: 0 32px;
          max-width: 274px;
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-about
          .wsc-header2020-dropdownMenu-body
          .wsc-header2020-dropdownMenuBody-item {
          padding: 0 24px;
          flex: none;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown-product .wsc-header2020-dropdownMenuBody-box,
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown-business .wsc-header2020-dropdownMenuBody-box,
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown-about .wsc-header2020-dropdownMenuBody-box {
          padding: 0;
          padding-right: 32px;
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-product
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-title,
        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-business
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-title {
          /* padding: 8px 0; */
          padding-bottom: 8px;
          /* padding-right: 32px; */
          /* margin-bottom: 24px; */
          /* border-bottom: 1px solid rgba(0, 0, 0, .2); */
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-product
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list,
        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-business
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list {
          margin: 0;
          font-size: 14px;
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-product
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li,
        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-business
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li {
          padding: 8px 0;
          color: #fff;
          /* padding-right: 32px; */
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-about
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li {
          padding: 0;
          font-size: 14px;
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-product
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li
          a,
        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-business
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li
          a,
        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-about
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li
          a {
          color: #444444;
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-product
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li
          a:hover,
        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-business
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li
          a:hover,
        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-about
          .wsc-header2020-dropdownMenuBody-box
          .wsc-header2020-dropdownMenuBody-list
          li
          a:hover {
          font-weight: 400;
          color: #000000;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-menu .wsc-header2020-dropdown-learnMore {
          padding: 24px 16px;
          border: 0;
          text-align: center;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-menu .wsc-header2020-dropdown-learnMore a:hover {
          font-weight: 400;
          text-decoration: none;
        }
      }

      @media (min-width: 1280px) {
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-collapse {
          background-color: transparent;
        }

        .wsc-header2020 .wsc-header202004-navbar-wondershare .wsc-header2020-navbarDropdown-menu {
          top: 0px;
          position: absolute;
          background: #666;
          z-index: -1;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuBody-item {
          max-width: 33%;
        }

        .wsc-header2020 .wsc-header2020-navbar-master {
          color: #ffffff;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown {
          position: static;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown::after,
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item:not(.log-out)::after {
          content: none;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle,
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-link {
          padding: 0 24px;
          color: #ffffff;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-link:hover {
          color: #ffffff;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-main .wsc-header2020-navbar-brand {
          padding: 16px 0;
        }

        /* .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle::before {
        background-color: rgba(0, 0, 0, .5);
        transition: none;
        z-index: -1;
        position: fixed;
        top: 0;
      } */

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-nav:first-child {
          display: flex;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-brand .div_a span {
          display: none;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-brand {
          padding: 2px 0;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-content {
          justify-content: center;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-item {
          border: none;
          flex: 0;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-box {
          padding: 5px 16px;
          box-shadow: none;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle {
          font-size: 16px;
          font-weight: 400;
          height: 80px;
          transition: none;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-list li a:hover {
          text-decoration: none;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item {
          padding: 0;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-link.active {
          background-color: transparent !important;
          color: #ffffff !important;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-toggle::after {
          position: relative;
        }

        /* .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle:hover::after, */
        .wsc-header2020 .wsc-header2020-navbarDropdown-toggle:hover::after,
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-link.active::after,
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-link:hover::after {
          content: "";
          /* 伪元素必需属性 */
          position: absolute;
          bottom: 50%;
          /* 定位在文字底部 */
          left: 50%;
          /* 水平居中开始 */
          transform: translate(-50%, 50%);
          /* 水平居中调整 */
          width: 114px;
          /* 宽度与文字内容相同 */
          height: 44px;
          /* 伪元素高度，可根据需要调整 */
          background: rgba(255, 255, 255, 0.08);
          /* 初始透明 */
          /* transition: background 0.3s ease; */
          border-radius: 8px;
          /* 添加过渡效果 */
          z-index: 0;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle[aria-expanded="true"] {
          /* background: rgba(0, 0, 0, 0.50);
        backdrop-filter: blur(25px); */
          color: #fff;
          font-weight: 400;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-toggle::before {
          transition: none;
        }

        .icon-weixin .icon-weixin-box {
          left: 50%;
        }

        .partner-content {
          margin-top: 24px;
        }

        .wsc-footer2020 .wsc-footer2020-top-content {
          border: none;
        }

        .wsc-footer2020 .wsc-footer2020-top {
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          border-top: 1px solid rgba(255, 255, 255, 0.2);
        }
      }

      @media (min-width: 1600px) {
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenu-body {
          /* padding: 27px 0; */
          padding-top: 18px;
          padding-bottom: 40px;
        }
      }

      @media (max-width: 1440px) {
        .wondershare-user-panel .ws-user-panel-dropdown {
          left: -100px;
        }
      }

      @media (max-width: 1366px) {
        .wondershare-user-panel .ws-user-panel-dropdown {
          left: -100px;
        }
      }

      @media (max-width: 1280px) {
        .wondershare-user-panel .ws-user-panel-dropdown {
          left: 0;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenuBody-box {
          padding-left: 0px;
        }

        .wsc-header2020-dropdownMenuBody-box {
          border: 0px;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-menu .wsc-header2020-container {
          padding: 0px 24px;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-toggle[aria-expanded="true"] + .wsc-header2020-navbarDropdown-menu {
          transition: none;
          background: transparent;
          backdrop-filter: blur(0px);
          box-shadow: none;
          padding-top: 0px;
        }

        .header-btn-drop {
          height: 40px;
          line-height: 38px;
          text-align: center;
          border-radius: 8px;
          text-transform: none;
          padding: 0;
          color: #fff;
          font-size: 14px;
          margin: 0;
          min-width: 320px;
          width: 100%;
          font-weight: 700;
        }

        .header-btn-drop-app {
          height: 40px;
          border-radius: 8px;
          text-transform: none;
          padding: 0;
          color: #fff;
          text-align: center;
          line-height: 38px;
          font-size: 14px;
          margin: 0;
          min-width: 180px;
          border: 1px solid #fff;
          margin-left: 0px;
          margin-top: 20px;
          font-weight: 700;
          width: 100%;
          display: block;
        }

        .header-btn-drop-app:hover {
          color: #000;
        }

        .header-btn-drop-app:hover .icon-get-app {
          display: none;
        }
      }

      @media (max-width: 991.98px) {
        .icon-weixin .icon-weixin-box[position-absolute="mobile-left"] {
          right: auto;
          left: 0;
          -webkit-transform: translateX(0);
          -moz-transform: translateX(0);
          transform: translateX(0);
        }

        .icon-weixin .icon-weixin-box[position-absolute="mobile-right"] {
          right: 0;
          left: auto;
          -webkit-transform: translateX(0);
          -moz-transform: translateX(0);
          transform: translateX(0);
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown-about .wsc-header2020-dropdownMenuBody-content {
          display: flex;
          flex-wrap: wrap;
        }

        .wsc-header2020
          .wsc-header2020-navbar-master
          .wsc-header2020-navbar-dropdown-about
          .wsc-header2020-dropdownMenuBody-content
          .wsc-header2020-dropdownMenuBody-item {
          width: 33.33%;
        }
      }

      @media (max-width: 767.98px) {
        .wsc-header2020 .wsc-header2020-container {
          padding-left: 24px;
          padding-right: 24px;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-linkBtn {
          display: block;
          font-size: 15px;
          height: 48px;
          line-height: 46px;
          padding: 0px;
          margin: 0px 24px;
          width: calc(100% - 48px);
        }

        .wsc-header2020 .wsc-header2020-navbar-nav {
          color: #fff;
          /* background: rgba(0, 0, 0, 0.50);
        backdrop-filter: blur(25px); */
          padding-top: 7px;
          height: calc(100vh - 48px);
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown-about .wsc-header2020-dropdownMenuBody-content {
          display: block;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-dropdown-about .wsc-header2020-dropdownMenuBody-box {
          margin: 0;
          padding: 4px 16px;
        }
      }
    </style>
    <!-- 脚文件样式 -->
    <style>
      .wsc-footer2020 .wsc-footer2020-container {
        padding-left: 15px;
        padding-right: 15px;
      }

      .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-language-pc {
        display: none;
      }

      .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-brand-logo {
        margin-bottom: 38px;
        margin-top: 8px;
      }

      .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-brand-logo img {
        width: 293.12px;
      }

      .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-social {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        /* padding: 29px 0; */
        border: 0;
        line-height: 24px;
        padding-top: 48px;
        padding-bottom: 21px;
        z-index: 6;
      }

      .wsc-footer2020 .wsc-footer2020-social .wsc-footer2020-social-title {
        white-space: nowrap;
        margin-bottom: 0;
        font-weight: 400;
        font-size: 14px;
      }

      .wsc-footer2020 .wsc-footer2020-social-box {
        position: absolute;
        bottom: 100px;
        display: flex;
        align-items: center;
        right: 104px;
        padding: 0px;
        line-height: 32px;
        z-index: 3;
      }

      .wsc-footer2020 .wsc-footer2020-social-box h5 {
        font-size: 14px;
        margin-bottom: 0;
        color: rgba(255, 255, 255, 0.6);
        font-weight: 400;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-menu ul {
        margin-top: -8px;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-item {
        padding: 8px 32px;
        border: 0;
        background-color: #000000;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-item .wsc-footer2020-subnav-link {
        color: #ffffff;
        opacity: 0.6;
      }

      .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-link .wsc-footer2020-subnav-img {
        display: block;
      }

      @media (min-width: 1280px) {
        .wsc-footer2020 .wsc-footer2020-bottom .wsc-footer2020-container .wsc-footer2020-copyright {
          padding: 20px 0;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-brand-logo img {
          width: 293.12px;
        }

        .wsc-footer2020 .wsc-footer2020-nav {
          width: 35.5%;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content {
          justify-content: space-between;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-subnav {
          padding: 3rem;
          padding-left: 112px;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-nav {
          order: 1;
          padding-top: 96px;
          padding-bottom: 100px;
          text-align: right;
          padding-right: 112px;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-link {
          font-size: 14px;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-toggle {
          padding: 0px;
        }

        .wsc-footer2020-get-app {
          padding-top: 8px;
        }

        .wsc-footer2020-get-app > div {
          border-radius: 9px;
          border: 1.5px solid #806fff;
          background: #fff;
          padding: 3px;
        }

        .wsc-footer2020-get-app p {
          color: rgba(255, 255, 255, 0.6);
          text-align: center;
          padding-top: 8px;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-toggle .wsc-footer2020-dropdown-title {
          font-size: 22px;
          font-weight: 700;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-menu ul {
          margin-top: 8px;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-item {
          padding: 8px 0;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-item .wsc-footer2020-subnav-link {
          opacity: 0.6;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-item .wsc-footer2020-subnav-link:hover {
          opacity: 1;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-language-pc {
          display: block;
        }

        .wsc-footer2020 .wsc-footer2020-subnav .wsc-footer2020-dropdown.wsc-footer2020-language {
          display: none;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-language-pc {
          margin-left: 64px;
          position: relative;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-language-pc .wsc-footer2020-dropdown-toggle {
          cursor: pointer;
          padding: 0;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-language-pc .wsc-footer2020-dropdown-toggle .wsc-footer2020-dropdown-title {
          font-size: 14px;
        }

        .wsc-footer2020
          .wsc-footer2020-container
          .wsc-footer2020-language-pc
          .wsc-footer2020-dropdown-toggle[aria-expanded="false"]
          + .wsc-footer2020-dropdown-menu {
          display: none !important;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-language-pc .wsc-footer2020-dropdown-toggle + .wsc-footer2020-dropdown-menu {
          position: absolute;
          width: 100%;
          top: -8px;
          left: 50%;
          transform: translate(-50%, -100%);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-language-pc .wsc-footer2020-dropdown-toggle + .wsc-footer2020-dropdown-menu ul {
          padding: 12px 0;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-language-pc .wsc-footer2020-dropdown-toggle + .wsc-footer2020-dropdown-menu ul li {
          padding: 4px 8px;
        }

        .wsc-footer2020
          .wsc-footer2020-container
          .wsc-footer2020-language-pc
          .wsc-footer2020-dropdown-toggle
          + .wsc-footer2020-dropdown-menu
          ul
          li
          .wsc-footer2020-subnav-link {
          font-size: 12px;
        }

        .wsc-footer2020
          .wsc-footer2020-container
          .wsc-footer2020-language-pc
          .wsc-footer2020-dropdown-toggle
          + .wsc-footer2020-dropdown-menu
          ul
          li:last-child
          .wsc-footer2020-subnav-link {
          opacity: 1;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-social {
          bottom: initial;
          left: initial;
          transform: none;
        }
      }

      @media (max-width: 767px) {
        .wsc-footer2020 .wsc-footer2020-dropdown-toggle[aria-expanded="true"] .wsc-footer2020-dropdown-icon {
          transform: rotate(-180deg);
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-menu ul {
          margin-bottom: 10px;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-dropdown-toggle {
          padding: 20px 24px;
          line-height: 20px;
        }

        .wsc-footer2020 .wsc-footer2020-dropdown-toggle .wsc-footer2020-dropdown-title {
          font-size: 15px;
          line-height: 20px;
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-brand-logo {
          margin-bottom: 95px;
          margin-top: 24px;
        }

        .wsc-footer2020 .wsc-footer2020-nav {
          border-bottom: 0px;
        }

        .wsc-footer2020 .wsc-footer2020-nav::after {
          content: "";
          width: calc(100% - 48px);
          height: 1px;
          background: rgba(255, 255, 255, 0.1);
          position: absolute;
          bottom: -1px;
          left: 50%;
          transform: translateX(-50%);
        }

        .wsc-footer2020 .wsc-footer2020-subnav .wsc-footer2020-dropdown {
          border-bottom: 0px;
        }

        .wsc-footer2020 .wsc-footer2020-subnav .wsc-footer2020-dropdown::after {
          content: "";
          width: calc(100% - 48px);
          height: 1px;
          background: rgba(255, 255, 255, 0.1);
          position: absolute;
          /* bottom: -1px; */
          left: 50%;
          transform: translateX(-50%);
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-brand-logo img {
          padding: 0px 15px;
          height: 40px;
        }

        .wsc-footer2020-subnav-content {
          padding-top: 12px;
        }

        .wsc-footer2020 .wsc-footer2020-subnav-content .wsc-footer2020-subnav-item {
          padding: 10px 24px;
          /* border-bottom: 1px solid rgba(0, 0, 0, 0.2); */
        }

        .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-social {
          padding: 28px 0 39px 0px;
        }
      }
    </style>
    <style>
      .ws-nps-form-type2 {
        z-index: 9999999999 !important;
      }

      .wsc-gotop.bg-primary {
        background: #000000;
      }

      .wsc-header2020 .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenu-content {
        flex-direction: column;
      }

      .wsc-header2020 .product-services .wsc-header2020-dropdownMenu-body .wsc-header2020-dropdownMenuBody-content {
        flex: 1;
      }

      .header_btn {
        background-color: #723fff !important;
        border-color: #723fff !important;
        color: #fff !important;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbar-link,
      .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-toggle {
        opacity: 1;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbar-link:hover,
      .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-toggle:hover {
        color: #723fff;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-toggle[aria-expanded="true"] {
        color: #723fff;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a {
        display: block;
        position: relative;
        color: #000000;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a > img {
        display: none;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a .text {
        text-align: left;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a .text h5 {
        display: flex;
        align-items: center;
        font-size: 16px;
        font-weight: 400;
        margin-bottom: 8px;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a .text h5 img {
        flex-shrink: 0;
        margin-left: 6px;
      }

      .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a:hover h5 {
        text-decoration: underline;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle,
      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item .wsc-header2020-navbar-link {
        font-size: 16px;
      }

      .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item .wsc-header2020-navbar-link svg {
        display: none;
      }

      @media (max-width: 768px) {
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-brand .div_a {
          width: 160px;
          height: 28px;
        }

        .wsc-footer2020 .wsc-footer2020-bottom .wsc-footer2020-copyright {
          padding-top: 28px;
          padding-bottom: 32px;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item .wsc-header2020-navbar-link svg {
          display: block;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuBody-list li {
          padding: 10px 0;
        }

        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbarDropdown-toggle,
        .wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-navbar-item .wsc-header2020-navbar-link {
          font-size: 15px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          line-height: 16px;
        }

        .wsc-header2020 .wsc-header2020-navbar-nav > li {
          font-size: 15px;
        }

        .wsc-header2020 .wsc-header202004-navbar-wondershare .wsc-header2020-navbar-link {
          height: auto;
          line-height: 1;
        }

        .wsc-header2020 .wsc-header2020-navbar-link,
        .wsc-header2020 .wsc-header2020-navbarDropdown-toggle {
          padding: 20px 24px;
          width: 100%;
        }

        .wsc-header2020-navbar-dropdown .navbarDropdownFirst {
          padding-top: 24px;
          padding-bottom: 20px;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuBody-title {
          display: block;
          margin-bottom: 0;
          min-height: 16px;
          padding-right: 0px;
          padding-top: 10px;
          padding-bottom: 10px;
        }
      }

      @media (min-width: 1280px) {
        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbar-brand .div_a {
          padding-right: 20px;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-toggle .wsc-header2020-dropdown-icon {
          display: flex;
          align-items: center;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-toggle svg {
          margin-left: 8px;
        }

        .wsc-header2020 .wsc-header2020-navbar-linkBtn-outline,
        .wsc-header2020 .wsc-header2020-navbar-linkBtn {
          margin: 0 0 0 32px;
          text-transform: none;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbar-link,
        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-toggle {
          padding: 12px 20px;
          color: #fff;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-menu {
          -webkit-backdrop-filter: none;
          backdrop-filter: none;
          position: relative;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuTabs-content {
          width: 240px;
          min-height: 286px;
          padding: 8px 0;
          padding-right: 30px;
          border-right: 1px solid #cccccc;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuTabs-content .wsc-header2020-dropdownMenu-tabs + div {
          padding-left: 16px;
          padding-bottom: 2px;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuTabs-content .wsc-header2020-dropdownMenu-tabs + div a {
          font-size: 14px;
          opacity: 0.6;
          transition: all 0.2s;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuTabs-content .wsc-header2020-dropdownMenu-tabs + div a:hover {
          opacity: 1;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuTabs-content .wsc-header2020-dropdownMenu-tabs .wsc-header2020-dropdownMenuTabs-item {
          padding: 0;
          margin-bottom: 4px;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuTabs-content .wsc-header2020-dropdownMenu-tabs .wsc-header2020-dropdownMenuTabs-nav {
          line-height: 1;
          padding: 8px 16px;
          opacity: 0.7;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuTabs-content .wsc-header2020-dropdownMenu-tabs .wsc-header2020-dropdownMenuTabs-nav[aria-expanded="true"] {
          font-size: 16px;
          background-color: rgba(0, 109, 255, 0.08);
          opacity: 1;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-menu .product-services {
          padding: 24px 0;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-menu .solution {
          padding: 48px 0;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenu-content {
          flex-direction: row;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenu-body {
          padding: 0;
        }

        .wsc-header2020-dropdownMenuBody-btn-group {
          margin-top: 24px;
        }

        .wsc-header2020 .product-services .wsc-header2020-dropdownMenu-body .wsc-header2020-dropdownMenuBody-content {
          justify-content: start;
          margin: 0;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenuBody-box {
          padding: 16px 48px;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenuBody-item {
          max-width: 32%;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-menu .solution .wsc-header2020-dropdownMenuBody-content {
          justify-content: flex-start;
          flex-wrap: wrap;
          margin: -15px;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-menu .solution .wsc-header2020-dropdownMenuBody-item {
          padding: 15px;
          flex: 0 0 25%;
          max-width: 25%;
        }

        .wsc-header2020
          .wsc-header2020-navbar-main
          .wsc-header2020-navbarDropdown-menu
          .solution
          .wsc-header2020-dropdownMenuBody-item
          .wsc-header2020-dropdownMenuBody-box {
          padding: 0;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenuBody-title {
          min-height: 36px;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenuBody-title .wsc-header2020-dropdownMenuBody-subtitle {
          font-weight: 700;
          line-height: 1;
          margin-bottom: 0;
        }

        .wsc-header2020 .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenuBody-title + .wsc-header2020-dropdownMenuBody-list {
          margin: -10px 0;
        }

        .wsc-header2020
          .wsc-header2020-navbarDropdown-menu
          .product-services
          .wsc-header2020-dropdownMenuBody-title
          + .wsc-header2020-dropdownMenuBody-list
          li {
          padding: 10px 0;
        }

        .wsc-header2020
          .wsc-header2020-navbarDropdown-menu
          .product-services
          .wsc-header2020-dropdownMenuBody-title
          + .wsc-header2020-dropdownMenuBody-list
          li
          a {
          display: inline-flex;
          align-items: center;
          opacity: 0.7;
        }

        .wsc-header2020
          .wsc-header2020-navbarDropdown-menu
          .product-services
          .wsc-header2020-dropdownMenuBody-title
          + .wsc-header2020-dropdownMenuBody-list
          li
          a:hover {
          color: #0056fb;
          opacity: 1;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a {
          color: #ffffff;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a > img {
          display: block;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a .text {
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 100%;
          text-align: center;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .solution .wsc-header2020-dropdownMenuBody-item .wsc-header2020-dropdownMenuBody-box a .text h5 {
          justify-content: center;
          font-size: 20px;
          font-weight: 700;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuBody-title {
          display: block;
          margin-bottom: 0;
          min-height: 20px;
          padding-right: 0px;
        }

        .wsc-header2020 .wsc-header2020-dropdownMenuBody-title:hover a {
          font-weight: 400;
        }

        .header-btn-drop {
          height: 40px;
          line-height: 36px;
          text-align: center;
          border-radius: 8px;
          text-transform: none;
          padding: 0;
          color: #fff;
          font-size: 14px;
          margin: 0;
          min-width: 320px;
          font-weight: 700;
        }

        .header-btn-drop-app {
          height: 40px;
          border-radius: 8px;
          text-transform: none;
          padding: 0;
          color: #fff;
          text-align: center;
          line-height: 38px;
          font-size: 14px;
          margin: 0;
          min-width: 180px;
          border: 1px solid #fff;
          margin-left: 16px;
          font-weight: 700;
        }

        .header-btn-drop-app:hover {
          color: #000;
        }

        .header-btn-drop-app:hover .icon-get-app {
          display: block;
        }
      }

      @media (min-width: 1600px) {
        .wsc-header2020 .product-services .wsc-header2020-container {
          max-width: 1232px;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenuBody-item {
          padding: 0 48px;
        }

        .wsc-header2020 .wsc-header2020-navbar-main .wsc-header2020-navbarDropdown-menu .product-services .wsc-header2020-dropdownMenuBody-box {
          padding: 16px 0;
        }
      }
    </style>
    <style>
      .ip_sure_content {
        color: #fff;
        background: rgba(23, 22, 26, 1);
        padding: 24px;
      }

      .ip_notice {
        font-weight: 700;
        font-size: 20px;
        line-height: 24px;
        margin-bottom: 8px;
      }

      .ip_notice_text {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 24px;
      }

      .ip_btn {
        background: rgba(114, 63, 255, 1);
        display: inline-block;
        font-weight: 400;
        font-size: 14px;
        text-align: center;
        line-height: 32px;
        height: 32px;
        text-align: center;
        color: #fff;
        border-radius: 6px;
        min-width: 132px;
        cursor: pointer;
      }

      .wsc-footer2020 .wsc-footer2020-container .wsc-footer2020-nav {
        position: relative;
      }

      .ws_link {
        position: absolute;
        display: inline-block;
        width: 160px;
        height: 56px;
        right: 50%;
        z-index: 1;
        /* background-color: #0056FB; */
      }

      .tm_link {
        position: absolute;
        display: inline-block;
        width: 133px;
        height: 56px;
        left: 50%;
        z-index: 1;
        /* background-color: #00ff00; */
      }

      .ws_link_header {
        position: absolute;
        display: inline-block;
        width: 112px !important;
        height: 32px;
        left: 0px;
        z-index: 21;
        bottom: 0px;
        /* background-color: #0056FB; */
      }

      .tm_link_header {
        position: absolute;
        display: inline-block;
        width: 86px !important;
        height: 32px;
        right: 0px;
        bottom: 0px;
        z-index: 21;
        /* background-color: #00ff00; */
      }

      @media (max-width: 768px) {
        .ws_link {
          position: absolute;
          display: inline-block;
          width: 106px;
          height: 40px;
          right: 50%;
          z-index: 1;
          /* background-color: #804000; */
          /* bottom: 0; */
        }

        .tm_link {
          position: absolute;
          display: inline-block;
          width: 106px;
          height: 40px;
          left: 50%;
          z-index: 1;
          /* bottom: 0; */
          /* background-color: #ff004c; */
        }

        .ws_link_header {
          position: absolute;
          display: inline-block;
          width: 80px !important;
          height: 32px;
          left: 0px;
          z-index: 21;
          /* background-color: #804000; */
          /* bottom: 0; */
        }

        .tm_link_header {
          position: absolute;
          display: inline-block;
          width: 80px !important;
          height: 32px;
          right: 0px;
          z-index: 21;
          /* bottom: 0; */
          /* background-color: #ff004c; */
        }
      }
    </style>
    <header class="wsc-header2020">
      <!-- 中文一级导航公共块文件 -->
      <nav class="wsc-header2020-navbar-master wsc-header202004-navbar-wondershare">
        <div class="wsc-header2020-container">
          <div class="wsc-header2020-navbar-content">
            <div class="wsc-header2020-navbar-brand position-relative">
              <i class="div_a"><span></span></i>
              <a rel="nofollow" class="ws_link_header" href="https://www.wondershare.com/" target="_blank"></a>
              <a rel="nofollow" class="tm_link_header" href="https://www.tomoviee.ai/"></a>
            </div>
            <button class="wsc-header2020-navbar-collapse-toggle" type="button" aria-expanded="false">
              <svg class="wsc-header2020-navbar-collapse-toggle-icon" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M4 6H20M20 12L4 12M20 18H4" stroke="white" stroke-width="1.5" />
              </svg>
              <svg class="wsc-header2020-navbar-collapse-toggle-icon-close" width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path d="M18 18L12 12M6 6L12 12M18 6L12 12M6 18L12 12" stroke="white" stroke-width="1.5" />
              </svg>
            </button>
            <div class="wsc-header2020-navbar-collapse">
              <ul class="wsc-header2020-navbar-nav" style="flex: auto">
                <li class="wsc-header2020-navbar-item">
                  <div class="d-flex align-items-center">
                    <!-- <div
                    style="opacity: 1; border-left: 1px solid #fff; padding: 0 0 0 20px; font-size: 12px; line-height: 1.6">
                    <span class="d-block">股票代码</span>
                    <span class="d-block">300624.SZ</span>
                  </div> -->
                  </div>
                </li>
              </ul>
              <ul class="wsc-header2020-navbar-nav">
                <!-- <li class="wsc-header2020-navbar-dropdown wsc-header2020-navbar-dropdown-product">
                <nav class="wsc-header2020-navbarDropdown-toggle navbarDropdownFirst" aria-expanded="false">
                  <a href="https://app.tomoviee.ai/"
                    style="width: 80%;height: 100%;color: #fff;text-decoration: none;display: flex;align-items: center;">
                    <span>Creative AI</span>
                  </a>
                  <div class="wsc-header2020-dropdown-icon">
                    <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 1L5 5L1 1" stroke="white" stroke-linecap="round" />
                    </svg>
                  </div>
                </nav>
                <div class="wsc-header2020-navbarDropdown-menu">
                  <div class="wsc-header2020-container">
                    <div class="wsc-header2020-dropdownMenu-content">
                      <div class="wsc-header2020-dropdownMenu-body">
                        <div class="wsc-header2020-dropdownMenuBody-content">
                          <div class="wsc-header2020-dropdownMenuBody-item">
                            <div class="wsc-header2020-dropdownMenuBody-box dropdownMenuBodyFirst">
                              <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                <a href="https://app.tomoviee.ai/video-generate/image-to-video" target="_blank"
                                  class="d-inline-flex align-items-center">
                                  <div class="text-white">Video</div>
                                </a>
                              </nav>
                              <ul class="wsc-header2020-dropdownMenuBody-list">
                                <li>
                                  <a href="https://app.tomoviee.ai/video-generate/text-to-video" target="_blank">
                                    <span>Text to Video</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/video-generate/image-to-video" target="_blank">
                                    <span>Image to Video</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/video-extend" target="_blank">
                                    <span>Video Extender</span>
                                  </a>
                                </li>
                              </ul>
                            </div>
                          </div>
                          <div class="wsc-header2020-dropdownMenuBody-item">
                            <div class="wsc-header2020-dropdownMenuBody-box ">
                              <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                <a href="https://app.tomoviee.ai/image-generate/" target="_blank"
                                  class="d-inline-flex align-items-center">
                                  <div class="text-white">Image</div>
                                </a>
                              </nav>
                              <ul class="wsc-header2020-dropdownMenuBody-list">
                                <li>
                                  <a href="https://app.tomoviee.ai/image-generate/" target="_blank">

                                    <span>Text to Image</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/partial-redraw" target="_blank">
                                    <span>AI Image Inpainting</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/image-generate/" target="_blank">
                                    <span>Image to Image</span>
                                  </a>
                                </li>
                              </ul>
                            </div>
                          </div>
                          <div class="wsc-header2020-dropdownMenuBody-item">
                            <div class="wsc-header2020-dropdownMenuBody-box">
                              <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                <a href="https://app.tomoviee.ai/audio-generate/text-to-music" target="_blank"
                                  class="d-inline-flex align-items-center">
                                  <div class="text-white">Audio</div>
                                </a>
                              </nav>
                              <ul class="wsc-header2020-dropdownMenuBody-list">
                                <li>
                                  <a href="https://app.tomoviee.ai/audio-generate/text-to-music" target="_blank">
                                    <span>Text to Music</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/audio-generate/text-to-sound" target="_blank">
                                    <span>Text to SFX</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/audio-generate/ai-soundtrack" target="_blank">
                                    <span>Auto-synced Audio</span>
                                  </a>
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
                        <div class="wsc-header2020-dropdownMenuBody-btn-group">
                          <a class="btn btn-action header-btn-drop" href="#" target="_blank">Create Free Now</a><a
                            class="btn btn-outline-white header-btn-drop-app position-relative" href="#"
                            target="_blank">Get APP
                            <div class="icon-get-app">
                              <div class="p-2">
                                <div>
                                  <img src="https://www.tomoviee.ai/images/home/<USER>" alt="tmapp" class="img-fluid"
                                    width="100">
                                </div>
                              </div>
                            </div>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li> -->
                <!-- <li class="wsc-header2020-navbar-dropdown wsc-header2020-navbar-dropdown-product">
                <nav class="wsc-header2020-navbarDropdown-toggle" aria-expanded="false">
                  <a
                    style="width: 80%;height: 100%;color: #fff;text-decoration: none;display: flex;align-items: center;">
                    <span>Effects</span>
                  </a>
                  <div class="wsc-header2020-dropdown-icon">
                    <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 1L5 5L1 1" stroke="white" stroke-linecap="round" />
                    </svg>
                  </div>
                </nav>
                <div class="wsc-header2020-navbarDropdown-menu">
                  <div class="wsc-header2020-container">
                    <div class="wsc-header2020-dropdownMenu-content">
                      <div class="wsc-header2020-dropdownMenu-body">
                        <div class="wsc-header2020-dropdownMenuBody-content">
                          <div class="wsc-header2020-dropdownMenuBody-item ">
                            <div class="wsc-header2020-dropdownMenuBody-box dropdownMenuBodyTwo">
                              <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                <a href="https://app.tomoviee.ai/video-generate/image-to-video" target="_blank"
                                  class="d-inline-flex align-items-center">
                                  <div class="text-white" style="min-width: 107px;">Bigfoot AI Vlog</div>
                                </a>
                              </nav>
                              <ul class="wsc-header2020-dropdownMenuBody-list">
                                <li>
                                  <a href="https://app.tomoviee.ai/video-generate/text-to-video" target="_blank">
                                    <span>AI Kissing</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/video-generate/image-to-video" target="_blank">
                                    <span>AI Hugging</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/video-extend" target="_blank">
                                    <span>Ghibli AI Art</span>
                                  </a>
                                </li>
                              </ul>
                            </div>
                          </div>
                          <div class="wsc-header2020-dropdownMenuBody-item">
                            <div class="wsc-header2020-dropdownMenuBody-box">
                              <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                <a href="https://app.tomoviee.ai/image-generate/" target="_blank"
                                  class="d-inline-flex align-items-center">
                                  <div class="text-white">Face Punch AI</div>
                                </a>
                              </nav>
                              <ul class="wsc-header2020-dropdownMenuBody-list">
                                <li>
                                  <a href="https://app.tomoviee.ai/image-generate/" target="_blank">

                                    <span>AI Mermaid Filter</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/partial-redraw" target="_blank">
                                    <span>AI Muscle Generator</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/image-generate/" target="_blank">
                                    <span>AI Squish Effect</span>
                                  </a>
                                </li>
                              </ul>
                            </div>
                          </div>
                          <div class="wsc-header2020-dropdownMenuBody-item">
                            <div class="wsc-header2020-dropdownMenuBody-box">
                              <nav class="wsc-header2020-dropdownMenuBody-title" aria-expanded="false">
                                <a href="https://app.tomoviee.ai/audio-generate/text-to-music" target="_blank"
                                  class="d-inline-flex align-items-center">
                                  <div class="text-white" style="min-width: 145px;">AI Couple Generator</div>
                                </a>
                              </nav>
                              <ul class="wsc-header2020-dropdownMenuBody-list">
                                <li>
                                  <a href="https://app.tomoviee.ai/audio-generate/text-to-music" target="_blank">
                                    <span>AI Hair Growth</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/audio-generate/text-to-sound" target="_blank">
                                    <span>AI Twerk Generator</span>
                                  </a>
                                </li>
                                <li>
                                  <a href="https://app.tomoviee.ai/audio-generate/ai-soundtrack" target="_blank">
                                    <span>AI Eye Zoom</span>
                                  </a>
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
                        <div class="wsc-header2020-dropdownMenuBody-btn-group">
                          <a class="btn btn-outline-white header-btn-drop" style="border: 1px solid #fff;" href="#"
                            target="_blank">Explore More Effects →</a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </li> -->

                <!-- <li class="wsc-header2020-navbar-item">
                <a rel="nofollow" class="wsc-header2020-navbar-link" style="min-width: 98px;text-align: center;"
                  href="https://www.tomoviee.ai/developers.html">API<svg width="6" height="10" viewBox="0 0 6 10"
                    fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1 1L5 5L1 9" stroke="white" stroke-opacity="0.6" stroke-linecap="round" />
                  </svg>
                </a>
              </li> -->
                <li class="wsc-header2020-navbar-item d-none d-md-block" style="visibility: hidden">
                  <a rel="nofollow" class="wsc-header2020-navbar-link" href="https://www.tomoviee.ai/pricing.html"
                    >Pricing<svg width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M1 1L5 5L1 9" stroke="white" stroke-opacity="0.6" stroke-linecap="round" />
                    </svg>
                  </a>
                </li>
                <li class="wsc-header2020-navbar-item wondershare-user-panel header_btn_panel">
                  <a class="wsc-header2020-navbar-linkBtn header_btn" href="#" target="_blank">
                    <span class="btn-text">Apply for Beta Access</span>
                  </a>
                </li>
                <li class="wsc-header2020-navbar-item wondershare-user-panel log-out d-none">
                  <a data-href="https://accounts.wondershare.com" data-source="109" class="wsc-header2020-navbar-linkBtn login-link font-weight-bold" href="#"
                    >logIn</a
                  >
                </li>
                <li class="wsc-header2020-navbar-item wondershare-user-panel log-in header_btn_panel">
                  <img class="avatar" src="https://images.wondershare.com/images2020/avatar-default.png" width="30" height="30" alt="avatar" />
                  <div class="ws-user-panel-dropdown">
                    <span class="ws-dropdown-item account_name"></span>
                    <a class="ws-dropdown-item account_url">Account Center</a>
                    <a class="ws-dropdown-item account_url_sign_out">Sign Out</a>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </nav>
    </header>
    <div class="modal fade p-0" id="ws_login_modal" tabindex="-1" aria-hidden="true">
      <div class="modal-dialog modal-dialog-centered" style="max-width: 28.5rem">
        <div class="modal-content border-0 overflow-hidden" style="box-shadow: none; border-radius: 0.625rem">
          <button
            type="button"
            data-dismiss="modal"
            aria-label="Close"
            class="close shadow-none font-size-normal position-absolute d-inline-flex align-items-center"
            style="z-index: 1; top: 0; right: 0; outline: none; padding: 1.125rem">
            <span class="wsc-icon wsc-icon-loaded d-inline-flex align-items-center" aria-hidden="true" style="height: 0.75rem">
              <svg
                class="wsc-svg-symbol-close"
                xmlns="https://www.w3.org/2000/svg"
                viewBox="0 0 32 32"
                width="32"
                height="32"
                fill="none"
                stroke="currentcolor"
                stroke-linecap="butt"
                stroke-linejoin="bevel"
                stroke-width="2">
                <path d="M2 30 L30 2 M30 30 L2 2"></path>
              </svg>
            </span>
          </button>
          <div id="login_frame_container" class="modal-body p-0" style="max-height: 631px">
            <iframe id="login_frame" src="" height="631" width="100%" frameborder="0"></iframe>
            <div class="w-100 flex-column justify-content-center align-items-center" style="display: flex; height: 631px">
              <div class="spinner-border text-primary" role="status" style="width: 6rem; height: 6rem"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal fade p-0" id="ip_sure_modal" tabindex="-1" aria-hidden="false" data-backdrop="static">
      <div class="modal-dialog modal-dialog-centered" style="max-width: 28.5rem">
        <div class="modal-content overflow-hidden" style="box-shadow: none; border-radius: 0.75rem; background-color: rgba(23, 22, 26, 1)">
          <button
            type="button"
            data-dismiss="modal"
            aria-label="Close"
            class="close shadow-none font-size-normal position-absolute d-inline-flex align-items-center"
            style="z-index: 1; top: 0; right: 0; outline: none; padding: 12px">
            <span class="wsc-icon wsc-icon-loaded d-inline-flex align-items-center" aria-hidden="true" style="height: 0.75rem">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 4L4 12M4 4L12 12" stroke="white" stroke-opacity="0.56" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </span>
          </button>
          <div class="modal-body ip_sure_content">
            <div class="ip_notice">Network Access Notice</div>
            <div class="ip_notice_text">
              Dear valued user, your current network location doesn’t match with Tomoviee’s region. For optimal features, subscriptions, and services, please
              select an option below:
            </div>
            <div class="text-right">
              <div class="ip_btn" onclick="ipBtn()">Global</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--天幕2.0头部-->
    <main>
      <section class="section-1">
        <div class="tianmu-top">
          <div class="banner-video">
            <div class="embed-responsive embed-responsive-custom">
              <video
                poster="https://www.tomoviee.ai/images/videopng/home.png"
                muted=""
                loop=""
                webkit-playsinline="true"
                autoplay=""
                playsinline="true"
                src="https://www.tomoviee.ai/videos/home/<USER>"></video>
            </div>
            <div class="top-content text-center position-absolute pb-4">
              <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/tomoviee-ai-horizontal-white2.svg" alt="tm" class="tianmu-logo" />
              <h1 class="h1title">From Imagination to Creation</h1>
              <div class="mt-1 mt-md-4 status_true" style="display: none">
                <a href="#" class="btn btn-lg btn-action rounded-12 sq-btn m-0" target="_self"
                  ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /><span class="btn-text">Join the Beta</span></a
                >
              </div>
              <div class="gc_box">
                <div class="status_text pb-3 pt-2 pt-md-3">
                  ToMoviee Beta: Join the Waitlist Now! Reserve your spot and get <br class="d-none d-md-block" />
                  500 bonus credits — generate up to 25 videos or 250 images.
                </div>
                <div class="status_text_sub mt-1 mb-3 status_ing">
                  We've received your application and wiill be reviewing it soon. <br class="d-none d-md-inline-block" />Your invitation code will be emailed to
                  you upon approval.
                </div>
                <div class="status_text_sub mt-1 mb-3 status_pass">
                  Invitation code sent!<br class="d-inline-block d-md-none" />
                  Please check the email you provided.
                </div>
                <div class="mt-1 mt-md-2 sq-btn-top">
                  <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 sq-btn m-0"
                    ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /><span class="btn-text">Apply for Beta Access</span></a
                  >
                </div>
                <div class="yqm_btn">
                  <a
                    href="#"
                    onclick="callCodeModal()"
                    class="text-decoration txyqm-btn-text h5 font-weight-normal text-white"
                    style="text-decoration: underline"
                    >Enter invitation code</a
                  >
                  <a href="#" onclick="callCodeModal()" class="btn btn-lg btn-action txyqm-btn rounded-12 sq-btn m-0" style="display: none"
                    >Enter invitation code</a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section-2 pt-md-5">
        <div class="container">
          <h2
            class="pt-0 mt-0 pt-md-4 mt-md-2 mb-2 display-3 font-weight-bold text-center subtitle"
            data-aos="fade-up"
            data-aos-mirror="true"
            data-aos-duration="1000"
            data-aos-delay="100">
            Create Without Limits
          </h2>
          <div
            class="text-center h5 font-weight-normal subdisc line-height-25 mb-md-0 h5sub mb-16"
            data-aos="fade-up"
            data-aos="slide-up"
            data-aos-mirror="true"
            data-aos-duration="1000"
            data-aos-delay="200">
            Where artificial intelligence <br class="d-block d-md-none" />
            powers your creative vision.
          </div>
          <div class="row align-items-center py-3 py-md-5">
            <div class="col-md-4 pt-4 pd-md-0 py-md-4 px-0">
              <div class="text-center text-md-left col-md-10">
                <div class="mb-3" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="100">
                  <img src="https://www.tomoviee.ai/images/home/<USER>" alt="va1" class="star2" />
                  <span class="text-color-gradient font-size-large img_text img_text">Text to Video</span>
                </div>
                <div class="h2 line-height-3rem pb-md-2 mb-md-1" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="200">
                  From Script to Scene, Instantly.
                </div>
                <div
                  class="h5 font-weight-normal subdisc line-height-2rem mb-1 mb-md-3"
                  data-aos="fade-up"
                  data-aos-mirror="true"
                  data-aos-duration="1000"
                  data-aos-delay="300">
                  Craft precise, lifelike stories with complete creative control. You direct, ToMoviee creates.
                </div>
                <div class="pt-3 d-none d-md-block" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="400">
                  <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                    ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                    <span class="btn-text">Apply for Beta Access</span></a
                  >
                </div>
              </div>
            </div>
            <div
              class="col-md-8 py-3 px-md-0 order-2 order-md-1 font-size-small"
              data-aos="fade-up"
              data-aos-mirror="true"
              data-aos-duration="1000"
              data-aos-delay="500">
              <div class="vv-container">
                <video
                  class="lazy-video border-radus-24"
                  muted=""
                  autoplay=""
                  loop=""
                  poster="https://www.tomoviee.ai/images/videopng/part1.png"
                  data-src="https://www.tomoviee.ai/videos/home/<USER>"
                  webkit-playsinline="true"
                  playsinline="true"></video>
              </div>
            </div>
          </div>
          <div class="d-block d-md-none" style="padding-bottom: 16px" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="400">
            <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
              ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Apply for Beta Access</span></a
            >
          </div>
          <div class="row align-items-center py-3 py-md-5">
            <div class="col-md-4 pt-4 pd-md-0 py-md-4 px-0">
              <div class="text-center text-md-left col-md-10">
                <div class="mb-3" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="300">
                  <img src="https://www.tomoviee.ai/images/home/<USER>" alt="va2" class="star2" />
                  <span class="text-color-gradient font-size-large img_text">Image to Video</span>
                </div>
                <div class="h2 line-height-3rem pb-md-2 mb-md-1" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="400">
                  Bring Still Images <br class="d-none d-md-block" />
                  to Life.
                </div>
                <div
                  class="h5 font-weight-normal subdisc line-height-2rem mb-1 mb-md-3"
                  data-aos="fade-up"
                  data-aos-mirror="true"
                  data-aos-duration="1000"
                  data-aos-delay="500">
                  Turn any image into a vivid, moving story <br class="d-block d-md-none" />
                  that grabs attention and sparks imagination.
                </div>
                <div class="pt-3 d-none d-md-block" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="500">
                  <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                    ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                    <span class="btn-text">Apply for Beta Access</span></a
                  >
                </div>
              </div>
            </div>
            <div
              class="col-md-8 pb-3 pt-2 px-md-0 order-2 order-md-1 font-size-small"
              data-aos="fade-up"
              data-aos-mirror="true"
              data-aos-duration="1000"
              data-aos-delay="500">
              <div class="vv-container">
                <video
                  class="lazy-video border-radus-24"
                  muted=""
                  autoplay=""
                  loop=""
                  poster="https://www.tomoviee.ai/images/videopng/part2.png"
                  data-src="https://www.tomoviee.ai/videos/home/<USER>"
                  webkit-playsinline="true"
                  playsinline="true"></video>
              </div>
            </div>
          </div>
          <div class="d-block d-md-none" style="padding-bottom: 16px" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="400">
            <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
              ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Apply for Beta Access</span></a
            >
          </div>
          <div class="row align-items-center py-3 py-md-5">
            <div class="col-md-4 pt-4 pd-md-0 py-md-4 px-0">
              <div class="text-center text-md-left col-md-10">
                <div class="mb-3" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="300">
                  <img src="https://www.tomoviee.ai/images/home/<USER>" alt="va3" class="star2" />
                  <span class="text-color-gradient font-size-large img_text">Video Extend</span>
                </div>
                <div class="h2 line-height-3rem pb-md-2 mb-md-1" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="400">
                  Continue Telling Your Story.
                </div>
                <div
                  class="h5 font-weight-normal subdisc line-height-2rem mb-1 mb-md-3"
                  data-aos="fade-up"
                  data-aos-mirror="true"
                  data-aos-duration="1000"
                  data-aos-delay="500">
                  Seamlessly extend your <br class="d-block d-md-none" />
                  story with smart, lifelike AI.
                </div>
                <div class="pt-3 d-none d-md-block" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="500">
                  <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                    ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                    <span class="btn-text">Apply for Beta Access</span></a
                  >
                </div>
              </div>
            </div>
            <div
              class="col-md-8 pb-3 pt-2 px-md-0 order-2 order-md-1 font-size-small"
              data-aos="fade-up"
              data-aos-mirror="true"
              data-aos-duration="1000"
              data-aos-delay="500">
              <div class="vv-container">
                <video
                  class="lazy-video border-radus-24"
                  muted=""
                  autoplay=""
                  loop=""
                  poster="https://www.tomoviee.ai/images/videopng/part3.png"
                  data-src="https://www.tomoviee.ai/videos/home/<USER>"
                  webkit-playsinline="true"
                  playsinline="true"></video>
              </div>
            </div>
          </div>
          <div class="d-block d-md-none" style="padding-bottom: 100px" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="400">
            <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
              ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Apply for Beta Access</span></a
            >
          </div>
        </div>
      </section>

      <div class="for_bg">
        <section class="section-3 pb-5 pb-md-0 mb-md-0 tianmu-main position-relative">
          <div class="pt-md-5 position-relative overflow-hidden dmx-box">
            <div class="container text-center pt-md-5">
              <div class="d-flex justify-content-center pb-4" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="100">
                <img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                <span class="text-color-gradient font-size-large img_text">AI Image Generation</span>
              </div>
              <h2
                class="mb-2 display-3 font-weight-bold text-center subtitle"
                data-aos="fade-up"
                data-aos-mirror="true"
                data-aos-duration="1000"
                data-aos-delay="100">
                Turn Your Imagination <br class="d-block d-md-none" />
                Into Reality.
              </h2>
              <div
                class="text-center h5 font-weight-normal subdisc line-height-25 mb-3 mb-md-0 h5sub"
                data-aos="fade-up"
                data-aos="slide-up"
                data-aos-mirror="true"
                data-aos-duration="1000"
                data-aos-delay="200">
                Generate images that are smarter,<br class="d-block d-md-none" />
                more realistic, and highly creative.
              </div>
              <div
                class="explore-nav mx-auto d-md-inline-block d-none pt-5"
                data-aos="fade-up"
                data-aos-mirror="true"
                data-aos-duration="1000"
                data-aos-delay="500">
                <div class="explore-tab d-flex">
                  <a href="javascript:;" class="tab-intro active">Text to Image</a>
                  <a href="javascript:;" class="tab-intro">Partial Repaint</a>
                  <a href="javascript:;" class="tab-intro">Reference to Image</a>
                </div>
              </div>
            </div>
            <div
              class="mt-md-3 pt-md-3 tianmu-scroll px-md-0 px-3 mx-md-0 mx-n3 overflow-hidden"
              data-aos="fade-up"
              data-aos-mirror="true"
              data-aos-duration="1000"
              data-aos-delay="700">
              <div class="swiper swiper-container pb-md-0 pb-5" id="tab-swiper1">
                <div class="swiper-wrapper">
                  <div class="swiper-slide">
                    <div
                      class="d-md-none d-flex align-items-center justify-content-center text-color-gradient"
                      style="margin-top: 12px; margin-bottom: 24px; font-size: 14px">
                      <img src="https://www.tomoviee.ai/images/home/<USER>" alt="text-icon" width="28" />
                      <span style="padding-left: 4px">Text to Image</span>
                    </div>
                    <div class="mobile-slide">
                      <div class="ws-video">
                        <video
                          class="lazy-video border-radus-24"
                          muted=""
                          loop=""
                          poster="https://www.tomoviee.ai/images/videopng/pic1.png"
                          data-src="https://www.tomoviee.ai/videos/home/<USER>"
                          webkit-playsinline="true"
                          playsinline="true"></video>
                      </div>
                    </div>
                  </div>
                  <div class="swiper-slide">
                    <div
                      class="d-md-none d-flex align-items-center justify-content-center text-color-gradient"
                      style="margin-top: 12px; margin-bottom: 24px; font-size: 14px">
                      <img src="https://www.tomoviee.ai/images/home/<USER>" alt="text-icon" width="28" />
                      <span style="padding-left: 4px">Partial Repaint</span>
                    </div>
                    <div class="mobile-slide">
                      <div class="ws-video">
                        <video
                          class="lazy-video border-radus-24"
                          muted=""
                          loop=""
                          poster="https://www.tomoviee.ai/images/videopng/pic2.png"
                          data-src="https://www.tomoviee.ai/videos/home/<USER>"
                          webkit-playsinline="true"
                          playsinline="true"></video>
                      </div>
                    </div>
                  </div>
                  <div class="swiper-slide">
                    <div
                      class="d-md-none d-flex align-items-center justify-content-center text-color-gradient"
                      style="margin-top: 12px; margin-bottom: 24px; font-size: 14px">
                      <img src="https://www.tomoviee.ai/images/home/<USER>" alt="text-icon" width="28" />
                      <span style="padding-left: 4px">Reference to Image</span>
                    </div>
                    <div class="mobile-slide">
                      <div class="ws-video">
                        <video
                          class="lazy-video border-radus-24"
                          muted=""
                          loop=""
                          poster="https://www.tomoviee.ai/images/videopng/pic3.png"
                          data-src="https://www.tomoviee.ai/videos/home/<USER>"
                          webkit-playsinline="true"
                          playsinline="true"></video>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="d-block d-md-none swiper-pagination swiperTab-pagination w-100"></div>
              </div>
              <div
                class="container d-block d-md-none"
                style="padding-top: 16px"
                data-aos="fade-up"
                data-aos-mirror="true"
                data-aos-duration="1000"
                data-aos-delay="400">
                <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                  ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Apply for Beta Access</span></a
                >
              </div>
            </div>
          </div>
        </section>
        <section class="mobile-section-4 d-block d-xl-none">
          <div class="container">
            <div class="swiper-container swiper video-swiper pb-md-0 pb-5">
              <div class="swiper-wrapper">
                <div class="swiper-slide">
                  <div class="mobile-slide">
                    <div class="text-center box-left">
                      <div class="mb-4">
                        <img src="https://www.tomoviee.ai/images/svgicon/8.svg" alt="star2" class="star2" />
                        <span class="text-color-gradient font-size-large img_text">Text to Music</span>
                      </div>
                      <div class="display-3 line-height-32 mb-1 font-weight-bold">A Melody Born From Words.</div>
                      <div class="mobile_subtitle mb-3">
                        Compose original, royalty-free <br class="d-block d-md-none" />
                        tracks in any style you want.
                      </div>
                    </div>
                    <div class="video-box-right">
                      <video
                        class="lazy-video border-radus-24"
                        muted=""
                        autoplay=""
                        loop=""
                        poster="https://www.tomoviee.ai/images/videopng/full1.png"
                        data-src="https://www.tomoviee.ai/videos/home/<USER>"
                        webkit-playsinline="true"
                        playsinline="true"></video>
                    </div>
                  </div>
                </div>
                <div class="swiper-slide">
                  <div class="mobile-slide">
                    <div class="text-center box-left">
                      <div class="mb-4">
                        <img src="https://www.tomoviee.ai/images/svgicon/9.svg" alt="star2" class="star2" />
                        <span class="text-color-gradient font-size-large img_text">Text to Sound Effects</span>
                      </div>
                      <div class="display-3 line-height-32 mb-1 font-weight-bold">Hear What You Imagine.</div>
                      <div class="mobile_subtitle mb-3">Generate HD sound effects from a single prompt. Fast, detailed, and studio-grade.</div>
                    </div>
                    <div class="video-box-right">
                      <video
                        class="lazy-video border-radus-24"
                        muted=""
                        autoplay=""
                        loop=""
                        poster="https://www.tomoviee.ai/images/videopng/full2.png"
                        data-src="https://www.tomoviee.ai/videos/home/<USER>"
                        webkit-playsinline="true"
                        playsinline="true"></video>
                    </div>
                  </div>
                </div>
                <div class="swiper-slide">
                  <div class="mobile-slide">
                    <div class="text-center box-left">
                      <div class="mb-4">
                        <img src="https://www.tomoviee.ai/images/svgicon/10.svg" alt="star2" class="star2" />
                        <span class="text-color-gradient font-size-large img_text">Text to Voice</span>
                      </div>
                      <div class="display-3 line-height-32 mb-1 font-weight-bold">Beyond Text-to-Speech.</div>
                      <div class="mobile_subtitle mb-3">Generate voices that feel real — with emotion, nuance, and custom tones.</div>
                    </div>
                    <div class="video-box-right">
                      <video
                        class="lazy-video border-radus-24"
                        muted=""
                        autoplay=""
                        loop=""
                        poster="https://www.tomoviee.ai/images/videopng/full3.png"
                        data-src="https://www.tomoviee.ai/videos/home/<USER>"
                        webkit-playsinline="true"
                        playsinline="true"></video>
                    </div>
                  </div>
                </div>
                <div class="swiper-slide">
                  <div class="mobile-slide">
                    <div class="text-center box-left">
                      <div class="mb-4">
                        <img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                        <span class="text-color-gradient font-size-large img_text">Auto BGM</span>
                      </div>
                      <div class="display-3 line-height-32 mb-1 font-weight-bold">Soundtracks That Move With Your Story</div>
                      <div class="mobile_subtitle mb-3">AI-generated scores that sync with your footage and amplify the emotional impact.</div>
                    </div>
                    <div class="video-box-right">
                      <video
                        class="lazy-video border-radus-24"
                        muted=""
                        autoplay=""
                        loop=""
                        poster="https://www.tomoviee.ai/images/videopng/full4.png"
                        data-src="https://www.tomoviee.ai/videos/home/<USER>"
                        webkit-playsinline="true"
                        playsinline="true"></video>
                    </div>
                  </div>
                </div>
              </div>
              <div class="audio-no-mobile"></div>
              <div class="swiper-pagination video-swiper-pagination w-100"></div>
            </div>
            <div class="pt-3 d-flex justify-content-center">
              <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Apply for Beta Access</span></a
              >
            </div>
            <!-- <div class="pt-4 pt-md-5 mt-0 mt-md-3 d-flex justify-content-center">
          <a href="#" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2">
            <span class="btn-text">Apply for Beta Access</span></a> -->
          </div>
        </section>
        <section class="section-4-new part-advantages text-left d-none d-xl-block">
          <div class="container advantages-wrap" style="margin-top: -40px">
            <div class="row">
              <div class="col-md-4 d-flex">
                <div class="advantages-list pt-md-5 pt-4">
                  <div class="list-item active">
                    <div class="text-md-left box-left">
                      <div class="mb-4" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="100">
                        <img src="https://www.tomoviee.ai/images/svgicon/8.svg" alt="star2" class="star2" />
                        <span class="text-color-gradient font-size-large img_text">Text to Music</span>
                      </div>
                      <div
                        class="display-3 line-height-4rem pb-2 font-weight-bold"
                        data-aos="fade-up"
                        data-aos-mirror="true"
                        data-aos-duration="1000"
                        data-aos-delay="200">
                        A Melody Born
                        <br />
                        From Words.
                      </div>
                      <div
                        class="h5 font-weight-normal subdisc line-height-2rem mb-2"
                        data-aos="fade-up"
                        data-aos-mirror="true"
                        data-aos-duration="1000"
                        data-aos-delay="300">
                        Compose original, royalty-free tracks in any style you want.
                      </div>
                      <div class="pt-3" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="300">
                        <a
                          href="#"
                          target="_blank"
                          class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                          ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                          <span class="btn-text">Apply for Beta Access</span></a
                        >
                      </div>
                    </div>
                  </div>
                  <div class="list-item">
                    <div class="text-md-left box-left">
                      <div class="mb-4" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="100">
                        <img src="https://www.tomoviee.ai/images/svgicon/9.svg" alt="star2" class="star2" />
                        <span class="text-color-gradient font-size-large img_text">Text to Sound Effects</span>
                      </div>
                      <div
                        class="display-3 line-height-4rem pb-2 font-weight-bold"
                        data-aos="fade-up"
                        data-aos-mirror="true"
                        data-aos-duration="1000"
                        data-aos-delay="200">
                        Hear What <br />You Imagine.
                      </div>
                      <div
                        class="h5 font-weight-normal subdisc line-height-2rem mb-2"
                        data-aos="fade-up"
                        data-aos-mirror="true"
                        data-aos-duration="1000"
                        data-aos-delay="300">
                        Generate HD sound effects from a single prompt. Fast, detailed, and studio-grade.
                      </div>
                      <div class="pt-3" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="300">
                        <a
                          href="#"
                          target="_blank"
                          class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                          ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                          <span class="btn-text">Apply for Beta Access</span></a
                        >
                      </div>
                    </div>
                  </div>
                  <div class="list-item">
                    <div class="text-md-left box-left">
                      <div class="mb-4" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="100">
                        <img src="https://www.tomoviee.ai/images/svgicon/10.svg" alt="star2" class="star2" />
                        <span class="text-color-gradient font-size-large img_text">Text to Voice</span>
                      </div>
                      <div
                        class="display-3 line-height-4rem pb-2 font-weight-bold"
                        data-aos="fade-up"
                        data-aos-mirror="true"
                        data-aos-duration="1000"
                        data-aos-delay="200">
                        Beyond<br />Text-to-Speech.
                      </div>
                      <div
                        class="h5 font-weight-normal subdisc line-height-2rem mb-2"
                        data-aos="fade-up"
                        data-aos-mirror="true"
                        data-aos-duration="1000"
                        data-aos-delay="300">
                        Generate voices that feel real — with emotion, nuance, and custom tones.
                      </div>
                      <div class="pt-3" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="300">
                        <a
                          href="#"
                          target="_blank"
                          class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                          ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                          <span class="btn-text">Apply for Beta Access</span></a
                        >
                      </div>
                    </div>
                  </div>
                  <div class="list-item">
                    <div class="text-md-left box-left">
                      <div class="mb-4" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="100">
                        <img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                        <span class="text-color-gradient font-size-large img_text">Auto BGM</span>
                      </div>
                      <div
                        class="display-3 line-height-4rem pb-2 font-weight-bold"
                        data-aos="fade-up"
                        data-aos-mirror="true"
                        data-aos-duration="1000"
                        data-aos-delay="200">
                        Soundtracks<br />
                        That Move<br />
                        With Your Story
                      </div>
                      <div
                        class="h5 font-weight-normal subdisc line-height-2rem mb-2"
                        data-aos="fade-up"
                        data-aos-mirror="true"
                        data-aos-duration="1000"
                        data-aos-delay="300">
                        AI-generated scores that sync with your footage and amplify the emotional impact.
                      </div>
                      <div class="pt-3" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="300">
                        <a
                          href="#"
                          target="_blank"
                          class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
                          ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" />
                          <span class="btn-text">Apply for Beta Access</span></a
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-8">
                <div class="sticky-wrapper d-flex align-items-center justify-content-between">
                  <div class="w-100 swiper swiper-container advantagesSwiper" style="margin-top: 40px">
                    <div class="audio-no"></div>
                    <div class="swiper-wrapper swiper-no-swiping">
                      <div class="swiper-slide">
                        <div class="video-box-right">
                          <video
                            class="lazy-video border-radus-24"
                            muted=""
                            autoplay=""
                            loop=""
                            poster="https://www.tomoviee.ai/images/videopng/full1.png"
                            data-src="https://www.tomoviee.ai/videos/home/<USER>"
                            webkit-playsinline="true"
                            playsinline="true"></video>
                        </div>
                      </div>
                      <div class="swiper-slide">
                        <div class="video-box-right">
                          <video
                            class="lazy-video border-radus-24"
                            muted=""
                            autoplay=""
                            loop=""
                            poster="https://www.tomoviee.ai/images/videopng/full2.png"
                            data-src="https://www.tomoviee.ai/videos/home/<USER>"
                            webkit-playsinline="true"
                            playsinline="true"></video>
                        </div>
                      </div>
                      <div class="swiper-slide">
                        <div class="video-box-right">
                          <video
                            class="lazy-video border-radus-24"
                            muted=""
                            autoplay=""
                            loop=""
                            poster="https://www.tomoviee.ai/images/videopng/full3.png"
                            data-src="https://www.tomoviee.ai/videos/home/<USER>"
                            webkit-playsinline="true"
                            playsinline="true"></video>
                        </div>
                      </div>
                      <div class="swiper-slide">
                        <div class="video-box-right">
                          <video
                            class="lazy-video border-radus-24"
                            muted=""
                            autoplay=""
                            loop=""
                            poster="https://www.tomoviee.ai/images/videopng/full4.png"
                            data-src="https://www.tomoviee.ai/videos/home/<USER>"
                            webkit-playsinline="true"
                            playsinline="true"></video>
                        </div>
                      </div>
                    </div>
                    <!-- <div class="swiper-pagination advantagesSwiper-pagination"></div> -->
                  </div>
                  <div class="swiper-pagination advantagesSwiper-pagination"></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
      <section class="section-5 py-5 pt-md-0">
        <div class="container pt-5 pt-md-0">
          <div class="limit_width">
            <h2
              class="pt-1 pt-md-0 display-3 font-weight-bold text-center subtitle"
              data-aos="fade-up"
              data-aos-mirror="true"
              data-aos-duration="1000"
              data-aos-delay="100">
              A New Era of Content Creation. <br class="d-none d-md-block" />Fast. Real. Controllable.
            </h2>
            <div
              class="text-center h5 font-weight-normal subdisc line-height-25 mb-0 h5sub"
              data-aos="fade-up"
              data-aos="slide-up"
              data-aos-mirror="true"
              data-aos-duration="1000"
              data-aos-delay="200"
              style="max-width: 730px; margin: auto">
              ToMoviee redefines AI creation with more realistic results, faster workflows, and fine-tuned control — across image, video, sound, and voice.
            </div>
          </div>
          <div class="row pt-5 d-none d-md-flex mt-3">
            <div class="col-md-4" style="max-width: 480px">
              <div class="card-ai-box" data-aos="fade-up" data-aos="slide-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="200">
                <div class="card-ai">
                  <video
                    class="w-100 lazy-video"
                    style="border-radius: 1.2rem"
                    muted=""
                    autoplay=""
                    loop=""
                    webkit-playsinline="true"
                    playsinline="true"
                    poster="https://www.tomoviee.ai/images/videopng/true1.png"
                    data-src="https://www.tomoviee.ai/videos/home/<USER>"></video>
                  <div class="card-content">
                    <div class="font-size-huge font-weight-bold pt-4 px-4 mx-2 line-height-25">Authenticity: AI That Feels Genuine</div>
                    <div class="font-size-small px-4 mx-2 card-desc pt-2 pb-4">
                      Our engine replicates lifelike motion, lighting, and audio for immersive storytelling. Every scene breathes with cinematic detail and
                      emotionally intelligent pacing, making AI-generated content indistinguishable from reality.
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4 my-md-0 my-3" style="max-width: 480px">
              <div class="card-ai-box" data-aos="fade-up" data-aos="slide-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="400">
                <div class="card-ai">
                  <video
                    class="w-100 lazy-video"
                    style="border-radius: 1.2rem"
                    muted=""
                    autoplay=""
                    loop=""
                    webkit-playsinline="true"
                    playsinline="true"
                    poster="https://www.tomoviee.ai/images/videopng/true2.png"
                    data-src="https://www.tomoviee.ai/videos/home/<USER>"></video>

                  <div class="card-content">
                    <div class="font-size-huge font-weight-bold pt-4 px-4 mx-2 line-height-25">Control: Precision at Your Fingertips</div>
                    <div class="font-size-small px-4 mx-2 card-desc pt-2 pb-4">
                      Fine-tune every element—camera movements, scene composition, and sound propagation. No more AI guesswork: your vision translates perfectly
                      from imagination to reality.
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-4" style="max-width: 480px">
              <div class="card-ai-box" data-aos="fade-up" data-aos="slide-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="600">
                <div class="card-ai">
                  <video
                    class="w-100 lazy-video"
                    style="border-radius: 1.2rem"
                    muted=""
                    autoplay=""
                    loop=""
                    webkit-playsinline="true"
                    playsinline="true"
                    poster="https://www.tomoviee.ai/images/videopng/true3.png"
                    data-src="https://www.tomoviee.ai/videos/home/<USER>"></video>
                  <div class="card-content">
                    <div class="font-size-huge font-weight-bold pt-4 px-4 mx-2 line-height-25">Speed: Streamline Your Creativity</div>
                    <div class="font-size-small px-4 mx-2 card-desc pt-2 pb-4">
                      Generate images, videos, and voices from text in one click. With 8x faster AI rendering and instant voice cloning, your ideas materialize
                      before inspiration fades.
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="swiper-container swiper video-swiper-s5 pb-md-0 pb-5 d-block d-md-none" style="padding-top: 32px">
            <div class="swiper-wrapper">
              <div class="swiper-slide">
                <div class="mobile-slide card-ai">
                  <video
                    class="w-100 lazy-video"
                    style="border-radius: 12px"
                    muted=""
                    autoplay=""
                    loop=""
                    webkit-playsinline="true"
                    playsinline="true"
                    poster="https://www.tomoviee.ai/images/videopng/true1.png"
                    data-src="https://www.tomoviee.ai/videos/home/<USER>"></video>
                  <div class="card-content text-center">
                    <div class="font-size-extra font-weight-bold pt-3 mx-2">
                      Authenticity: <br />
                      AI That Feels Genuine
                    </div>
                    <div class="font-size-small px-4 card-desc pt-1">
                      Our engine replicates lifelike motion, lighting, and audio for immersive storytelling. Every scene breathes with cinematic detail and
                      emotionally intelligent pacing, making AI-generated content indistinguishable from reality.
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="mobile-slide card-ai">
                  <video
                    class="w-100 lazy-video"
                    style="border-radius: 12px"
                    muted=""
                    autoplay=""
                    loop=""
                    webkit-playsinline="true"
                    playsinline="true"
                    poster="https://www.tomoviee.ai/images/videopng/true2.png"
                    data-src="https://www.tomoviee.ai/videos/home/<USER>"></video>
                  <div class="card-content text-center">
                    <div class="font-size-extra font-weight-bold pt-3 mx-2">
                      Control: <br />
                      Precision at Your Fingertips
                    </div>
                    <div class="font-size-small px-4 card-desc pt-1">
                      Fine-tune every element—camera movements, scene composition, and sound propagation. No more AI guesswork: your vision translates perfectly
                      from imagination to reality.
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="mobile-slide card-ai">
                  <video
                    class="w-100 lazy-video"
                    style="border-radius: 12px"
                    muted=""
                    autoplay=""
                    loop=""
                    webkit-playsinline="true"
                    playsinline="true"
                    poster="https://www.tomoviee.ai/images/videopng/true3.png"
                    data-src="https://www.tomoviee.ai/videos/home/<USER>"></video>
                  <div class="card-content text-center">
                    <div class="font-size-extra font-weight-bold pt-3 mx-2">
                      Speed:<br />
                      Streamline Your Creativity
                    </div>
                    <div class="font-size-small px-4 card-desc pt-1">
                      Generate images, videos, and voices from text in one click. With 8x faster AI rendering and instant voice cloning, your ideas materialize
                      before inspiration fades.
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="d-block d-md-none swiper-pagination video-swiper-s5-pagination w-100"></div>
          </div>
          <div class="pt-4 pt-md-5 mt-0 d-flex justify-content-center" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="100">
            <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
              ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Apply for Beta Access</span></a
            >
          </div>
        </div>
      </section>

      <div class="d-block d-md-none" style="padding-top: 61.6px"></div>
      <section class="section-6 create-box pb-md-5 pt-md-5">
        <div class="container py-md-5">
          <div class="limit_width2">
            <h2
              class="pt-md-3 mb-2 display-3 font-weight-bold text-center subtitle"
              data-aos="fade-up"
              data-aos-mirror="true"
              data-aos-duration="1000"
              data-aos-delay="100">
              Your Creative Workflow, Supercharged by AI
            </h2>
            <div
              class="text-center h5 font-weight-normal subdisc line-height-25 mb-0 h5sub"
              data-aos="fade-up"
              data-aos="slide-up"
              data-aos-mirror="true"
              data-aos-duration="1000"
              data-aos-delay="200"
              style="margin: auto">
              From rapid ideation to scalable production, ToMoviee helps you build <br class="d-none d-md-block" />
              efficient, personalized workflows that keep creativity flowing.
            </div>
          </div>
        </div>
        <div class="part-album" id="partAlbum">
          <div class="mask_mao_top">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <div class="mask_mao_bot">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
          </div>
          <div class="mask_top"></div>
          <div class="mask_bottom"></div>
          <div class="mask_btn_box" style="margin: auto" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="100">
            <a
              href="#"
              target="_blank"
              class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn position_btn"
              ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Create Your Own</span></a
            >
          </div>
          <div class="album-box1">
            <!-- 1 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-1.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001Q-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 2 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-2.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001R-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 3 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-3.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001S-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 4 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-4.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001T-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>

            <!-- 5 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-5.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001U-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 6 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-6.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001V-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 7 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-7.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001W-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 8 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-8.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001X-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 9 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-9.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001Y-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
          </div>
          <div class="album-box2">
            <!-- 10 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-10.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1001Z-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 11 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-11.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10021-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 12 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-12.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10022-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 13 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-13.png" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10023-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 14 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-14.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10024-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 15 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-15.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10025-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 16 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-16.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10026-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 17 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-17.png" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10027-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 18 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-18.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10028-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
          </div>
          <div class="album-box3">
            <!-- 19 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-19.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/10029-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 20 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-20.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002a-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 21 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-21.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002b-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 22 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-22.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002c-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 23 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-23.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002d-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 24 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-24.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002e-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 25 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-25.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002f-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 26 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-26.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002g-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 27 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-27.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002j-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
          </div>

          <div class="album-box4 d-none d-md-flex">
            <!-- 28 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-28.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002l-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 29 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-29.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002m-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 30 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-30.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002n-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 31 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-31.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002o-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 32 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-32.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002p-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 33 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-33.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002q-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 34 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-34.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002r-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 35 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-35.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002s-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 36 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-36.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002t-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
          </div>
          <div class="album-box5 d-none d-md-flex">
            <!-- 37 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-37.png" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002u-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 38 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-38.png" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002w-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 39 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-39.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002x-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 40 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-40.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002y-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 41 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-41.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002z-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 42 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-42.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002A-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 43 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-43.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002B-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 44 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-44.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002C-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 45 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-45.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002D-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 46 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-46.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002E-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
            <!-- 47 -->
            <div class="album-img rounded-8 overflow-hidden text-center text-white">
              <img src="https://www.tomoviee.ai/images/home/<USER>/new-inspire-47.jpeg" alt="album-img" class="w-100" />
              <div class="album-title">
                <div class="d-flex align-items-center justify-content-between">
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> ws room </span>
                  <span class="d-flex align-items-center"><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> 28</span>
                </div>
              </div>
              <div class="album-btn">
                <a href="https://app.tomoviee.ai/s/1002F-1" target="_blank" class="btn btn-md rounded-12 m-0"> Create Your Own</a>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section-7 container pt-5 mt-md-3 overflow-hidden part-scroll-animation">
        <div class="d-block d-md-none" style="padding-top: 23.2px"></div>
        <div class="row pt-5">
          <div class="col-md-4 text-center text-md-left">
            <div class="col-md-12">
              <h2
                class="pt-md-4 mt-md-2 mb-2 mb-md-4 display-3 font-weight-bold subtitle text-white"
                data-aos="fade-up"
                data-aos-mirror="true"
                data-aos-duration="1000"
                data-aos-delay="100">
                A Creative <br class="d-block d-md-none" />
                Platform for All
              </h2>
            </div>
            <div class="col-md-11">
              <div
                class="h5 font-weight-normal subdisc line-height-25 mb-4 mb-md-0 h5sub"
                data-aos="fade-up"
                data-aos-mirror="true"
                data-aos-duration="1000"
                data-aos-delay="200"
                style="max-width: 534px; margin: auto">
                From solo creators to enterprise teams—ToMoviee delivers tailored AI solutions for video, imagery, and audio. Conquer social content, brand
                campaigns, and cinematic projects in one powerful ecosystem.
              </div>
            </div>
          </div>
          <div class="col-md-8 d-none d-md-block">
            <div class="scroll-animation-container flex-container">
              <div class="item col-6 with-scroll">
                <div
                  class="position-relative"
                  style="margin-bottom: 1.875rem"
                  data-aos="fade-up"
                  data-aos-mirror="true"
                  data-aos-duration="1000"
                  data-aos-delay="200">
                  <img src="https://www.tomoviee.ai/images/home/<USER>" loading="lazy" class="img-fluid w-100 border-radus-24" alt="ai-technology" />
                  <div class="position-absolute px-3 px-md-4 pos_bot">
                    <h4 class="px-2 mb-2 pb-1 font-size-huge">Content Creators:</h4>
                    <p class="font-size-small px-2 m-0" style="line-height: 22px">Never miss a trend again.</p>
                    <p class="font-size-small px-2 m-0" style="opacity: 0.7; line-height: 22px">
                      ✓ 10x content output without quality drop <br />✓ Create top-performing content instantly <br />✓ Dominate daily posting across social
                      media
                    </p>
                  </div>
                </div>
                <div
                  class="position-relative"
                  style="margin-bottom: 1.875rem"
                  data-aos="fade-up"
                  data-aos-mirror="true"
                  data-aos-duration="1000"
                  data-aos-delay="500">
                  <img src="https://www.tomoviee.ai/images/home/<USER>" loading="lazy" class="img-fluid w-100 border-radus-24" alt="ai-technology" />
                  <div class="position-absolute px-3 px-md-4 pos_bot">
                    <h4 class="px-2 mb-2 pb-1 font-size-huge">Design Teams:</h4>
                    <p class="font-size-small px-2 m-0" style="line-height: 22px">From concept to KV, faster than ever.</p>
                    <p class="font-size-small px-2 m-0" style="opacity: 0.7; line-height: 22px">
                      ✓ Generate images/videos/audio in one creative suite <br />
                      ✓ Accelerate client wins with efficient visual design <br />
                      ✓ Built-in creative community for endless inspiration
                    </p>
                  </div>
                </div>
              </div>
              <div class="item col-6 with-scroll" style="margin-top: 140px">
                <div
                  class="position-relative"
                  style="margin-bottom: 1.875rem"
                  data-aos="fade-up"
                  data-aos-mirror="true"
                  data-aos-duration="1000"
                  data-aos-delay="200">
                  <img src="https://www.tomoviee.ai/images/home/<USER>" loading="lazy" class="img-fluid w-100 border-radus-24" alt="ai-technology" />
                  <div class="position-absolute px-3 px-md-4 pos_bot">
                    <h4 class="px-2 mb-2 pb-1 font-size-huge">Brand & Marketing Teams:</h4>
                    <p class="font-size-small px-2 m-0" style="line-height: 22px">Turn words into winning campaigns.</p>
                    <p class="font-size-small px-2 m-0" style="opacity: 0.7; line-height: 22px">
                      ✓ From copy to video, audio, and visuals <br />
                      ✓ Multi-modal generation in one workflow <br />
                      ✓ Boost output, consistency, and quality
                    </p>
                  </div>
                </div>
                <div
                  class="position-relative"
                  style="margin-bottom: 1.875rem"
                  data-aos="fade-up"
                  data-aos-mirror="true"
                  data-aos-duration="1000"
                  data-aos-delay="500">
                  <img src="https://www.tomoviee.ai/images/home/<USER>" loading="lazy" class="img-fluid w-100 border-radus-24" alt="ai-technology" />
                  <div class="position-absolute px-3 px-md-4 pos_bot">
                    <h4 class="px-2 mb-2 pb-1 font-size-huge">Filmmakers & Video Editors:<br /></h4>
                    <p class="font-size-small px-2 m-0" style="line-height: 22px">AI that understands cinematic language:</p>
                    <p class="font-size-small px-2 m-0" style="opacity: 0.7; line-height: 22px">
                      ✓ Control camera moves (dolly/pan/tilt) via text prompts <br />
                      ✓ AI-crafted flow and music tailored to your mood <br />
                      ✓ Render 4K footage 8x faster with physics-accurate lighting
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="swiper-container swiper img-swiper-s7 pb-md-0 d-block d-md-none position-relative" style="padding-bottom: 38px">
          <div class="swiper-wrapper">
            <div class="swiper-slide">
              <div class="mobile-slide position-relative">
                <img src="https://www.tomoviee.ai/images/home/<USER>" loading="lazy" class="img-fluid w-100 border-radus-24" alt="ai-technology" />
                <div class="position-absolute px-3 px-md-4 text-center pos_bot">
                  <h4 class="px-3 pb-1">Content Creators:</h4>
                  <p class="font-size-small m-0" style="padding: 0px; line-height: 22px">Never miss a trend again.</p>
                  <p class="font-size-small m-0" style="opacity: 0.7; padding: 0px; line-height: 22px; margin: auto">
                    ✓ 10x content output without quality drop <br />✓ Create top-performing content instantly <br />✓ Dominate daily posting across social media
                  </p>
                </div>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="mobile-slide position-relative">
                <img src="https://www.tomoviee.ai/images/home/<USER>" loading="lazy" class="img-fluid w-100 border-radus-24" alt="ai-technology" />
                <div class="position-absolute px-3 px-md-4 text-center pos_bot">
                  <h4 class="px-3 pb-1">Design Teams:</h4>
                  <p class="font-size-small m-0" style="padding: 0px; line-height: 22px">From concept to KV, faster than ever.</p>
                  <p class="font-size-small m-0" style="opacity: 0.7; padding: 0px; line-height: 22px">
                    ✓ Generate images/videos/audio in one creative suite <br />
                    ✓ Accelerate client wins with efficient visual design <br />
                    ✓ Built-in creative community for endless inspiration
                  </p>
                </div>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="mobile-slide position-relative">
                <img src="https://www.tomoviee.ai/images/home/<USER>" loading="lazy" class="img-fluid w-100 border-radus-24" alt="ai-technology" />
                <div class="position-absolute px-3 px-md-4 text-center pos_bot">
                  <h4 class="px-3 pb-1">Brand & Marketing Teams:</h4>
                  <p class="font-size-small m-0" style="padding: 0px; line-height: 22px">Turn words into winning campaigns.</p>
                  <p class="font-size-small m-0" style="opacity: 0.7; padding: 0px; line-height: 22px">
                    ✓ From copy to video, audio, and visuals <br />
                    ✓ Multi-modal generation in one workflow <br />
                    ✓ Boost output, consistency, and quality
                  </p>
                </div>
              </div>
            </div>
            <div class="swiper-slide">
              <div class="mobile-slide position-relative">
                <img src="https://www.tomoviee.ai/images/home/<USER>" loading="lazy" class="img-fluid w-100 border-radus-24" alt="ai-technology" />
                <div class="position-absolute px-3 px-md-4 text-center pos_bot">
                  <h4 class="px-3 pb-1">Filmmakers & Video Editors:</h4>
                  <p class="font-size-small m-0" style="padding: 0px; line-height: 22px">AI that understands cinematic language:</p>
                  <p class="font-size-small m-0" style="opacity: 0.7; padding: 0px; line-height: 22px">
                    ✓ Control camera moves (dolly/pan/tilt) via text prompts <br />
                    ✓ AI-crafted flow and music tailored to your mood <br />
                    ✓ Render 4K footage 8x faster with physics-accurate lighting
                  </p>
                </div>
              </div>
            </div>
          </div>
          <div class="d-block d-md-none swiper-pagination img-swiper-s7-pagination w-100"></div>
        </div>
        <div
          class="d-flex d-md-none justify-content-center"
          data-aos="fade-up"
          data-aos-mirror="true"
          data-aos-duration="1000"
          data-aos-delay="100"
          style="padding-top: 12px">
          <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
            ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Apply for Beta Access</span></a
          >
        </div>
      </section>
      <section class="section-8 pt-4 pb-5 py-md-5 mt-4">
        <div class="pt-md-1"></div>
        <div class="container pt-md-4 mt-2 mt-md-4">
          <h2
            class="pt-5 mt-4 pt-md-5 mt-md-5 display-3 font-weight-bold text-center subtitle mb-36"
            data-aos="fade-up"
            data-aos-mirror="true"
            data-aos-duration="1000"
            data-aos-delay="100">
            The Creative Engine <br class="d-block d-md-none" />
            Behind It All
          </h2>
          <!-- <div class="pb-3 d-none d-md-block"></div> -->
          <div class="row position-relative ai_color_box d-none d-xl-flex overflow-hidden">
            <canvas id="canvas"></canvas>
            <div class="col-md-4 ai_box_left">
              <div class="ai_left">
                <div style="height: 50%"></div>
                <div class="d-flex align-items-center ai_left_content">
                  <div style="max-width: 275px">
                    <img src="https://www.tomoviee.ai/images/home/<USER>" alt="w1" width="40" />
                    <h5 class="py-3 m-0 h5Title">All-in-One AI Creation</h5>
                    <div class="font-size-small subdisc line-height-28">
                      Fully integrated text → image → video → audio pipelines. Empower your storytelling from start to end.
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="col-md-8 d-flex flex-column ai_box_right">
              <div class="ai_top d-flex align-items-center mt-3 mt-md-0">
                <div class="ai_top_content">
                  <img src="https://www.tomoviee.ai/images/home/<USER>" alt="w2" width="40" />
                  <h5 class="py-3 m-0 h5Title">Efficient Creative Flow</h5>
                  <div class="font-size-small subdisc line-height-28">
                    From inspiration to distribution, generate, edit, manage, and publish content in a seamless, efficient flow.
                  </div>
                </div>
              </div>
              <div class="ai_bottom">
                <div class="row h-100">
                  <div class="col-md-6 d-flex flex-column justify-content-center">
                    <div class="ai_bl d-flex align-items-center glowing-border">
                      <div class="ai_bl_content">
                        <img src="https://www.tomoviee.ai/images/home/<USER>" alt="w3" width="40" />
                        <h5 class="py-3 m-0 h5Title">Real World Simulation</h5>
                        <div class="font-size-small subdisc line-height-28">
                          AI models trained to simulate real-world movement, lighting, and audio — solving the “uncanny valley” problem of traditional
                          generators.
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="col-md-6 d-flex flex-column justify-content-center">
                    <!-- 右下内容 -->
                    <div class="ai_br d-flex align-items-center">
                      <div class="ai_br_content">
                        <img src="https://www.tomoviee.ai/images/home/<USER>" alt="w4" width="40" />
                        <h5 class="py-3 m-0 h5Title">Creative Precision</h5>
                        <div class="font-size-small subdisc line-height-28">
                          Control every detail with text prompts, reference images, or partial editing. From camera angles to facial expressions — it’s your
                          call.
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="swiper-container swiper img-swiper-s8 pb-md-0 pb-5 d-block d-xl-none position-relative">
            <div class="swiper-wrapper">
              <div class="swiper-slide">
                <div class="mobile-slide position-relative">
                  <div class="ai_mobile d-flex align-items-center justify-content-center">
                    <div class="ai_mobile_content">
                      <img src="https://www.tomoviee.ai/images/home/<USER>" alt="w1" width="40" />
                      <h5 class="py-3 m-0 h5Title">All-in-One AI Creation</h5>
                      <div class="font-size-small subdisc line-height-28">
                        Fully integrated text → image → video → audio pipelines. Empower your storytelling from start to end.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="mobile-slide position-relative">
                  <div class="ai_mobile d-flex align-items-center justify-content-center">
                    <div class="ai_mobile_content">
                      <img src="https://www.tomoviee.ai/images/home/<USER>" alt="w2" width="40" />
                      <h5 class="py-3 m-0 h5Title">Efficient Creative Flow</h5>
                      <div class="font-size-small subdisc line-height-28">
                        From inspiration to distribution, generate, edit, manage, and publish content in a seamless, efficient flow.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="mobile-slide position-relative">
                  <div class="ai_mobile d-flex align-items-center justify-content-center">
                    <div class="ai_mobile_content">
                      <img src="https://www.tomoviee.ai/images/home/<USER>" alt="w3" width="40" />
                      <h5 class="py-3 m-0 h5Title">Real World Simulation</h5>
                      <div class="font-size-small subdisc line-height-28">
                        AI models trained to simulate real-world movement, lighting, and audio — solving the “uncanny valley” problem of traditional generators.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="swiper-slide">
                <div class="mobile-slide position-relative">
                  <div class="ai_mobile d-flex align-items-center justify-content-center">
                    <div class="ai_mobile_content">
                      <img src="https://www.tomoviee.ai/images/home/<USER>" alt="w4" width="40" />
                      <h5 class="py-3 m-0 h5Title">Creative Precision</h5>
                      <div class="font-size-small subdisc line-height-28">
                        Control every detail with text prompts, reference images, or partial editing. From camera angles to facial expressions — it’s your call.
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="d-block d-md-none swiper-pagination img-swiper-s8-pagination w-100"></div>
          </div>

          <div
            class="pt-4 pt-md-3 mt-0 mt-md-3 pb-3 d-flex justify-content-center aos-init aos-animate"
            data-aos="fade-up"
            data-aos-mirror="true"
            data-aos-duration="1000"
            data-aos-delay="100">
            <a href="#" target="_blank" class="btn btn-lg btn-action rounded-12 w-100 m-0 d-flex align-items-center justify-content-center content-btn"
              ><img src="https://www.tomoviee.ai/images/home/<USER>" alt="star2" class="star2" /> <span class="btn-text">Apply for Beta Access</span></a
            >
          </div>
        </div>
      </section>
      <div class="pb-md-3"></div>
      <section class="section-9 pt-0 mt-0 py-md-5 mt-md-5">
        <div class="container part-faq">
          <div class="row">
            <div class="col-md-4 text-center text-md-left">
              <div class="col-md-12">
                <h2
                  class="display-3 font-weight-bold subtitle text-white"
                  style="margin-bottom: 48px"
                  data-aos="fade-up"
                  data-aos-mirror="true"
                  data-aos-duration="1000"
                  data-aos-delay="100">
                  Frequently <br />
                  Asked <br class="d-none d-md-block" />
                  Questions
                </h2>
              </div>
            </div>
            <div class="col-md-8 text-left" data-aos="fade-up" data-aos-mirror="true" data-aos-duration="1000" data-aos-delay="200">
              <div id="accordion-faq" class="px-md-0" role="tablist">
                <div class="faq-option">
                  <div
                    id="heading-faq1"
                    class="faq-item d-flex justify-content-between with-hand collapsed"
                    data-toggle="collapse"
                    data-target="#collapse-faq1"
                    aria-expanded="false"
                    aria-controls="collapse-faq1"
                    role="tab">
                    Is AI-generated content copyright-free?
                    <i class="wsc-icon h-auto ml-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M19.5 9L12 16.5L4.5 9" stroke="#C0C0C0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M4.5 15L12 7.5L19.5 15" stroke="#F0F0F0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </i>
                  </div>
                  <div
                    id="collapse-faq1"
                    class="collapse pb-4 pb-md-0 secondFaq"
                    aria-labelledby="heading-faq1"
                    data-parent="#accordion-faq"
                    style="color: rgba(255, 255, 255, 0.6)">
                    Copyright laws for AI-generated content vary by jurisdiction. Ownership may depend on factors like your prompts, uploaded assets, or local
                    regulations. While we transfer all potential rights arising from our service to you, we strongly advise consulting legal counsel before
                    commercial use.
                    <br />Key Conditions:
                    <ul>
                      <li>You must ensure inputs don’t infringe third-party rights.</li>
                      <li>You’re responsible for legal compliance when using outputs.</li>
                      <li>
                        Rights granted are subject to our
                        <a href="https://www.tomoviee.ai/policies/terms-of-use.html" class="text-white" style="text-decoration: underline">Terms of Use</a> and
                        applicable laws.
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="faq-option">
                  <div
                    id="heading-faq2"
                    class="faq-item d-flex justify-content-between with-hand collapsed"
                    data-toggle="collapse"
                    data-target="#collapse-faq2"
                    aria-expanded="false"
                    aria-controls="collapse-faq2"
                    role="tab">
                    How is my data used?
                    <i class="wsc-icon h-auto ml-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M19.5 9L12 16.5L4.5 9" stroke="#C0C0C0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M4.5 15L12 7.5L19.5 15" stroke="#F0F0F0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </i>
                  </div>
                  <div
                    id="collapse-faq2"
                    class="collapse pb-4 pb-md-0 secondFaq"
                    aria-labelledby="heading-faq2"
                    data-parent="#accordion-faq"
                    style="color: rgba(255, 255, 255, 0.6)">
                    Your privacy and data security are our top priorities. We collect, process, and store personal information only as required by law — and we
                    never sell, trade, or rent your data to third parties for marketing purposes. AI features are only activated when you explicitly choose to
                    use them. These features operate based solely on the content you provide and do not access other private data unless you input it directly.
                    For full details, please review our
                    <a href="https://www.wondershare.com/privacy.html" class="text-white" style="text-decoration: underline">Policy</a>.
                  </div>
                </div>
                <div class="faq-option">
                  <div
                    id="heading-faq3"
                    class="faq-item d-flex justify-content-between with-hand collapsed"
                    data-toggle="collapse"
                    data-target="#collapse-faq3"
                    aria-expanded="false"
                    aria-controls="collapse-faq3"
                    role="tab">
                    What creation capabilities does ToMoviee offer?
                    <i class="wsc-icon h-auto ml-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M19.5 9L12 16.5L4.5 9" stroke="#C0C0C0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M4.5 15L12 7.5L19.5 15" stroke="#F0F0F0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </i>
                  </div>
                  <div
                    id="collapse-faq3"
                    class="collapse pb-4 pb-md-0 secondFaq"
                    aria-labelledby="heading-faq3"
                    data-parent="#accordion-faq"
                    style="color: rgba(255, 255, 255, 0.6)">
                    Tomoviee AI provides a full-stack AI content generation capability spanning images, videos, audio, and speech. Its modules include
                    text-to-video, image-to-video, video continuation, text-to-image, partial repainting, image-to-image, text-to-music, text-to-sound effects,
                    video scoring, and text-to-speech. This comprehensive suite covers the efficient creative needs of content creators across multiple
                    dimensions including image, video, and audio.
                  </div>
                </div>
                <div class="faq-option">
                  <div
                    id="heading-faq4"
                    class="faq-item d-flex justify-content-between with-hand collapsed"
                    data-toggle="collapse"
                    data-target="#collapse-faq4"
                    aria-expanded="false"
                    aria-controls="collapse-faq4"
                    role="tab">
                    What should I avoid when using AI features?
                    <i class="wsc-icon h-auto ml-3">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M19.5 9L12 16.5L4.5 9" stroke="#C0C0C0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M4.5 15L12 7.5L19.5 15" stroke="#F0F0F0" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                    </i>
                  </div>
                  <div
                    id="collapse-faq4"
                    class="collapse pb-4 pb-md-0 secondFaq"
                    aria-labelledby="heading-faq4"
                    data-parent="#accordion-faq"
                    style="color: rgba(255, 255, 255, 0.6)">
                    When using ToMoviee’s AI capabilities, you must ensure that your inputs comply with all relevant laws, ethical standards, and community
                    norms. Avoid submitting content that infringes upon the rights of others or violates our platform’s terms. For full usage guidelines, please
                    refer to our <a href="https://www.wondershare.com/privacy.html" class="text-white" style="text-decoration: underline">Policy</a>.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      <section class="section-mobile-10 d-block d-md-none">
        <div>
          <a href="#" target="_balnk" style="display: inline-block">
            <video
              poster="https://www.tomoviee.ai/images/videopng/end_mobile.png"
              data-src="https://www.tomoviee.ai/videos/home/<USER>"
              muted
              webkit-playsinline="true"
              autoplay
              playsinline="true"
              class="w-100 lazy-video bot_mobile_video"></video>
          </a>
        </div>
      </section>
      <section class="section-10 pt-4 pt-md-1 mt-md-0 d-none d-md-block">
        <div class="pb-md-3">
          <div class="pt-3 bt-video-container position-relative">
            <a href="#" target="_balnk" style="display: inline-block">
              <video
                poster="https://www.tomoviee.ai/images/videopng/endw.png"
                data-src="https://www.tomoviee.ai/videos/home/<USER>"
                muted
                webkit-playsinline="true"
                autoplay
                playsinline="true"
                class="w-100 lazy-video bot_video"></video>
            </a>
          </div>
        </div>
      </section>
      <div class="modal fade p-0" id="invite_code_modal" tabindex="-1" aria-hidden="true" data-backdrop="static">
        <div class="modal-dialog modal-dialog-centered" style="max-width: 40rem">
          <div
            class="modal-content overflow-hidden"
            style="box-shadow: none; border-radius: 0.75rem; border: 1px solid #ffffff14; background: rgba(23, 22, 26, 1)">
            <button
              type="button"
              data-dismiss="modal"
              aria-label="Close"
              class="close shadow-none font-size-normal position-absolute d-inline-flex align-items-center"
              style="z-index: 1; top: 0; right: 0; outline: none; padding: 1.125rem">
              <span class="wsc-icon wsc-icon-loaded d-inline-flex align-items-center" aria-hidden="true" style="height: 0.75rem">
                <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_2481_318942)">
                    <path
                      d="M19.6478 5.98873C19.9404 6.28164 19.9406 6.75658 19.6478 7.04939L13.8142 12.883L19.6471 18.716C19.94 19.0089 19.94 19.4837 19.6471 19.7766C19.3542 20.0692 18.8792 20.0694 18.5864 19.7766L12.7535 13.9437L6.91987 19.7773L6.86255 19.8291C6.56807 20.0688 6.13364 20.0515 5.85921 19.7773C5.58481 19.5029 5.56767 19.0685 5.80742 18.774L5.85921 18.7167L11.6928 12.883L5.85852 7.0487C5.56599 6.75589 5.56602 6.28087 5.85852 5.98804C6.15132 5.69524 6.62626 5.69542 6.91918 5.98804L12.7535 11.8224L18.5871 5.98873C18.88 5.69584 19.3549 5.69584 19.6478 5.98873Z"
                      fill="white" />
                  </g>
                  <defs>
                    <clipPath id="clip0_2481_318942">
                      <rect width="24" height="24" fill="white" transform="translate(0.75 0.171875)" />
                    </clipPath>
                  </defs>
                </svg>
              </span>
            </button>
            <div class="modal-body p-0 invite_code_content text-center">
              <div class="code_pic"></div>
              <div class="code_title pb-2 px-3">
                Just one more step to start using <br />
                ToMoviee Studio!
              </div>
              <div class="code_title_sub pb-2">Enjoy 500 free credits instantly <br class="d-inline-block d-md-none" />+ 30/day for logging in</div>
              <div class="code_form">
                <form class="p-2">
                  <div class="form-group">
                    <input type="text" class="form-control is-invalid" class="" id="yqm" placeholder="Enter your code" required="" />
                    <div class="invalid-feedback"></div>
                  </div>
                </form>
                <div class="int_btnbox">
                  <a href="#" class="inv-btn btn btn-sm btn-action" style="font-size: 14px !important; border-radius: 8px">Activate Now</a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
    <div class="parameter" data-toggle="gotop" data-type="ws2020"></div>
    <footer class="wsc-footer2020">
      <div class="wsc-footer2020-top">
        <div class="wsc-footer2020-container">
          <div class="wsc-footer2020-top-content">
            <div class="wsc-footer2020-subnav">
              <div class="wsc-footer2020-subnav-content">
                <div class="wsc-footer2020-dropdown">
                  <nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                    <h5 class="wsc-footer2020-dropdown-title">Get Started</h5>
                    <div class="wsc-footer2020-dropdown-icon">
                      <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L5 5L1 1" stroke="white" stroke-linecap="round"></path>
                      </svg>
                    </div>
                  </nav>
                  <div class="wsc-footer2020-dropdown-menu">
                    <ul>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://app.tomoviee.ai/create/text-to-video" target="_blank">Text to Video</a>
                      </li>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://app.tomoviee.ai/create/image-to-video" target="_blank">Image to Video</a>
                      </li>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://app.tomoviee.ai/create/video-extend" target="_blank">AI Video Extender</a>
                      </li>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://app.tomoviee.ai/create/text-to-image" target="_blank">AI Image Generator</a>
                      </li>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://app.tomoviee.ai/create/text-to-music" target="_blank">AI Audio Generator</a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="wsc-footer2020-dropdown position-relative">
                  <nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                    <h5 class="wsc-footer2020-dropdown-title">For Enterprise</h5>
                    <div class="wsc-footer2020-dropdown-icon">
                      <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L5 5L1 1" stroke="white" stroke-linecap="round"></path>
                      </svg>
                    </div>
                  </nav>
                  <div class="wsc-footer2020-dropdown-menu">
                    <ul>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://www.tomoviee.ai/developers.html">AI Engine</a>
                      </li>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://www.tomoviee.ai/doc/guide/platform-introduction/introduce.html">API Docs</a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="wsc-footer2020-dropdown">
                  <nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                    <h5 class="wsc-footer2020-dropdown-title">Company</h5>
                    <div class="wsc-footer2020-dropdown-icon">
                      <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L5 5L1 1" stroke="white" stroke-linecap="round"></path>
                      </svg>
                    </div>
                  </nav>
                  <div class="wsc-footer2020-dropdown-menu">
                    <ul>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/about/" target="_blank">About Us</a>
                      </li>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://www.tomoviee.ai/guide.html" target="_blank">Guide</a>
                      </li>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://www.tomoviee.ai/policies.html">Policies</a>
                      </li>
                      <li class="wsc-footer2020-subnav-item">
                        <a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/about/contact-us.html">Contact Us</a>
                      </li>
                    </ul>
                  </div>
                </div>
                <div class="wsc-footer2020-get-app d-none d-md-block">
                  <!--<div>-->
                  <!--  <img src="https://www.tomoviee.ai/images/home/<USER>" alt="tmapp" class="img-fluid" width="88">-->
                  <!--</div>-->
                  <!--<p class="m-0 font-size-tiny">Get Mobile App</p>-->
                </div>
                <div class="wsc-footer2020-dropdown wsc-footer2020-language">
                  <nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                    <h5 class="wsc-footer2020-dropdown-title d-flex align-items-center">
                      <span>English</span>
                    </h5>
                    <div class="wsc-footer2020-dropdown-icon">
                      <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 1L5 5L1 1" stroke="white" stroke-linecap="round"></path>
                      </svg>
                    </div>
                  </nav>
                  <div class="wsc-footer2020-dropdown-menu">
                    <ul>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/">English</a></li>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.de/">Deutsch</a></li>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.fr/">Français</a></li>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.it/">Italiano</a></li>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.es/">Español</a></li>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com.br/">Português</a></li>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.jp/">日本語</a></li>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.kr/">한국어</a></li>
                      <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link">简体中文</a></li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <div class="wsc-footer2020-social">
              <div class="d-flex align-items-center wsc-footer2020-social-item d-xl-none" style="gap: 4px">
                <a href="https://x.com/tomoviee_ai" target="_blank" class="text-white">
                  <i class="wsc-icon">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M31.2327 12.887H35.0747L26.6811 22.2059L36.5555 34.887H28.8227L22.767 27.196L15.8379 34.887H11.9936L20.9715 24.9193L11.5 12.887H19.4268L24.9006 19.9168L31.2304 12.887H31.2327ZM29.8843 32.6531H32.0132L18.2711 15.0035H15.9866L29.8843 32.6531Z"
                        fill="white"
                        fill-opacity="0.6" />
                    </svg>
                  </i>
                </a>
                <a href="https://ca.pinterest.com/ToMovieeAI/" target="_blank" class="text-white">
                  <i class="wsc-icon">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M22.8257 12.1311C18.9298 12.5767 15.0476 15.8062 14.8874 20.4195C14.7867 23.2362 15.5672 25.3493 18.1835 25.9427C19.3189 23.8905 17.8173 23.4379 17.5838 21.9533C16.6247 15.8696 24.4325 11.7207 28.5184 15.9681C31.3454 18.9091 29.4844 27.9573 24.9247 27.0168C20.5572 26.1186 27.0626 18.9161 23.5765 17.5019C20.7427 16.3527 19.2365 21.0175 20.5801 23.3347C19.7927 27.3194 18.0966 31.0742 18.7833 36.0721C21.0105 34.4163 21.7613 31.2455 22.377 27.9386C23.4963 28.6351 24.0938 29.3598 25.5221 29.4724C30.7891 29.8899 33.7305 24.0852 33.0118 18.7308C32.3731 13.9839 27.7493 11.5682 22.8257 12.1311Z"
                        fill="white"
                        fill-opacity="0.6" />
                    </svg>
                  </i>
                </a>
                <a href="https://www.instagram.com/tomoviee.ai/" target="_blank" class="text-white">
                  <i class="wsc-icon">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M20.0054 24.0614C20.0054 21.8504 21.7968 20.0576 24.0072 20.0576C26.2176 20.0576 28.0099 21.8504 28.0099 24.0614C28.0099 26.2725 26.2176 28.0653 24.0072 28.0653C21.7968 28.0653 20.0054 26.2725 20.0054 24.0614ZM17.8416 24.0614C17.8416 27.4677 20.602 30.2288 24.0072 30.2288C27.4124 30.2288 30.1728 27.4677 30.1728 24.0614C30.1728 20.6552 27.4124 17.8941 24.0072 17.8941C20.602 17.8941 17.8416 20.6552 17.8416 24.0614ZM28.976 17.6496C28.9759 17.9346 29.0603 18.2133 29.2185 18.4504C29.3767 18.6875 29.6017 18.8723 29.8649 18.9815C30.1281 19.0907 30.4178 19.1194 30.6974 19.0639C30.9769 19.0084 31.2337 18.8712 31.4353 18.6697C31.6369 18.4682 31.7742 18.2115 31.8299 17.9319C31.8856 17.6523 31.8572 17.3625 31.7482 17.0991C31.6393 16.8357 31.4547 16.6106 31.2178 16.4521C30.9809 16.2936 30.7024 16.209 30.4174 16.2089H30.4168C30.0349 16.2091 29.6686 16.3609 29.3984 16.631C29.1282 16.9011 28.9763 17.2675 28.976 17.6496ZM19.1563 33.8378C17.9856 33.7845 17.3494 33.5895 16.9265 33.4247C16.3659 33.2064 15.9659 32.9464 15.5454 32.5263C15.1249 32.1062 14.8645 31.7065 14.6473 31.1457C14.4824 30.7229 14.2874 30.0863 14.2342 28.9153C14.176 27.6493 14.1644 27.269 14.1644 24.0615C14.1644 20.8541 14.177 20.4749 14.2342 19.2078C14.2875 18.0368 14.484 17.4014 14.6473 16.9774C14.8655 16.4166 15.1254 16.0165 15.5454 15.5958C15.9654 15.1752 16.365 14.9148 16.9265 14.6975C17.3492 14.5326 17.9856 14.3375 19.1563 14.2843C20.4219 14.2261 20.8021 14.2144 24.0072 14.2144C27.2123 14.2144 27.5928 14.227 28.8596 14.2843C30.0302 14.3376 30.6654 14.5341 31.0893 14.6975C31.6499 14.9148 32.0499 15.1758 32.4705 15.5958C32.891 16.0159 33.1503 16.4166 33.3686 16.9774C33.5334 17.4001 33.7284 18.0368 33.7816 19.2078C33.8398 20.4749 33.8515 20.8541 33.8515 24.0615C33.8515 27.269 33.8398 27.6482 33.7816 28.9153C33.7283 30.0863 33.5324 30.7228 33.3686 31.1457C33.1503 31.7065 32.8904 32.1066 32.4705 32.5263C32.0505 32.946 31.6499 33.2064 31.0893 33.4247C30.6667 33.5896 30.0302 33.7846 28.8596 33.8378C27.5939 33.8961 27.2137 33.9077 24.0072 33.9077C20.8007 33.9077 20.4216 33.8961 19.1563 33.8378ZM19.0569 12.1235C17.7786 12.1817 16.9052 12.3845 16.1424 12.6814C15.3524 12.988 14.6837 13.3993 14.0154 14.0667C13.3471 14.7342 12.9369 15.4042 12.6304 16.1944C12.3336 16.9579 12.1309 17.8311 12.0727 19.1097C12.0135 20.3903 12 20.7997 12 24.0614C12 27.3232 12.0135 27.7326 12.0727 29.0132C12.1309 30.2919 12.3336 31.165 12.6304 31.9285C12.9369 32.7183 13.3472 33.389 14.0154 34.0561C14.6836 34.7233 15.3524 35.134 16.1424 35.4415C16.9066 35.7384 17.7786 35.9411 19.0569 35.9994C20.3378 36.0576 20.7464 36.0721 24.0072 36.0721C27.268 36.0721 27.6773 36.0586 28.9575 35.9994C30.2359 35.9411 31.1087 35.7384 31.872 35.4415C32.6615 35.134 33.3307 34.7236 33.999 34.0561C34.6673 33.3887 35.0766 32.7183 35.384 31.9285C35.6808 31.165 35.8844 30.2918 35.9417 29.0132C35.9999 27.7316 36.0134 27.3232 36.0134 24.0614C36.0134 20.7997 35.9999 20.3903 35.9417 19.1097C35.8835 17.831 35.6808 16.9574 35.384 16.1944C35.0766 15.4046 34.6662 14.7352 33.999 14.0667C33.3318 13.3983 32.6615 12.988 31.873 12.6814C31.1087 12.3845 30.2358 12.1808 28.9585 12.1235C27.6782 12.0653 27.2689 12.0508 24.0082 12.0508C20.7474 12.0508 20.3378 12.0643 19.0569 12.1235Z"
                        fill="white"
                        fill-opacity="0.6" />
                    </svg>
                  </i>
                </a>
                <a href="https://www.youtube.com/channel/UCKUHjbkaNn9z2XTqLD1ZWHw" target="_blank" class="text-white">
                  <i class="wsc-icon">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M24.2402 14.4001C24.2402 14.4001 32.7979 14.3998 34.9307 14.9714C36.1101 15.29 37.0372 16.2201 37.3506 17.3982C37.9176 19.5249 37.9209 23.952 37.9209 23.9998C37.9209 23.9998 37.9207 28.4641 37.3506 30.6023C37.0329 31.7847 36.1057 32.7138 34.9307 33.0281C32.7979 33.5997 24.2402 33.6003 24.2402 33.6003C24.1809 33.6003 15.6804 33.5977 13.5508 33.0281C12.3713 32.7095 11.4442 31.7805 11.1309 30.6023C10.5607 28.4641 10.5605 23.9998 10.5605 23.9998C10.5606 23.952 10.5638 19.5249 11.1309 17.3982C11.4486 16.2156 12.3756 15.2856 13.5508 14.9714C15.6806 14.4018 24.1809 14.4002 24.2402 14.4001ZM21.5068 28.115L28.6143 23.9998L21.5068 19.8855V28.115Z"
                        fill="white"
                        fill-opacity="0.6" />
                    </svg>
                  </i>
                </a>
                <a href="https://www.tiktok.com/@tomoviee_ai" target="_blank" class="text-white">
                  <i class="wsc-icon">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M31.5298 17.1031C31.3706 17.0208 31.2156 16.9306 31.0654 16.8328C30.6286 16.544 30.2281 16.2037 29.8726 15.8193C28.983 14.8015 28.6508 13.7689 28.5284 13.046H28.5333C28.4311 12.4459 28.4734 12.0576 28.4797 12.0576H24.4281V27.7247C24.4281 27.9351 24.4281 28.143 24.4192 28.3484C24.4192 28.374 24.4168 28.3976 24.4153 28.4251C24.4153 28.4364 24.4153 28.4482 24.4128 28.46C24.4128 28.4629 24.4128 28.4659 24.4128 28.4688C24.3701 29.031 24.1899 29.574 23.8881 30.0501C23.5863 30.5263 23.172 30.921 22.6819 31.1994C22.171 31.4901 21.5932 31.6425 21.0055 31.6418C19.1177 31.6418 17.5878 30.1025 17.5878 28.2015C17.5878 26.3005 19.1177 24.7612 21.0055 24.7612C21.3628 24.7608 21.7179 24.8171 22.0577 24.9278L22.0626 20.8024C21.0312 20.6691 19.9834 20.7511 18.9852 21.0431C17.987 21.3351 17.0602 21.8308 16.2632 22.4989C15.5649 23.1057 14.9778 23.8297 14.5284 24.6383C14.3573 24.9332 13.712 26.1181 13.6339 28.0412C13.5847 29.1328 13.9125 30.2637 14.0688 30.7311V30.7409C14.1671 31.0161 14.548 31.9553 15.1687 32.7471C15.6693 33.3822 16.2606 33.9401 16.9238 34.4029V34.393L16.9336 34.4029C18.8951 35.7357 21.0698 35.6482 21.0698 35.6482C21.4463 35.633 22.7074 35.6482 24.1396 34.9695C25.728 34.2171 26.6323 33.096 26.6323 33.096C27.21 32.4262 27.6694 31.6628 27.9907 30.8387C28.3574 29.8749 28.4797 28.719 28.4797 28.257V19.9452C28.5289 19.9747 29.1835 20.4077 29.1835 20.4077C29.1835 20.4077 30.1267 21.0122 31.5981 21.4059C32.6538 21.686 34.0761 21.745 34.0761 21.745V17.7228C33.5778 17.7769 32.5658 17.6196 31.5298 17.1031Z"
                        fill="white"
                        fill-opacity="0.6" />
                    </svg>
                  </i>
                </a>
                <a href="https://discord.gg/nqeukDYf7h" target="_blank" class="text-white">
                  <i class="wsc-icon">
                    <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path
                        d="M32.7522 16.1328C35.5805 20.3207 36.9773 25.0445 36.4552 30.4828C36.453 30.5058 36.4411 30.5269 36.4223 30.5408C34.2805 32.1246 32.2053 33.0859 30.1593 33.7233C30.1433 33.7282 30.1263 33.7279 30.1105 33.7226C30.0947 33.7172 30.081 33.707 30.0713 33.6934C29.5986 33.031 29.1691 32.3327 28.7928 31.5994C28.7712 31.5562 28.791 31.5042 28.8354 31.4872C29.5175 31.2284 30.1662 30.9181 30.7901 30.5509C30.8392 30.5219 30.8423 30.4509 30.7969 30.4169C30.6645 30.3179 30.5334 30.2139 30.4078 30.1098C30.3843 30.0906 30.3527 30.0868 30.3261 30.0997C26.2757 31.9834 21.8386 31.9834 17.7402 30.0997C17.7136 30.0878 17.682 30.0919 17.6591 30.1108C17.5339 30.2148 17.4025 30.3179 17.2713 30.4169C17.2259 30.4509 17.2297 30.5219 17.2791 30.5509C17.903 30.9112 18.5516 31.2284 19.2328 31.4885C19.277 31.5055 19.2979 31.5562 19.276 31.5994C18.9079 32.3337 18.4784 33.032 17.9969 33.6943C17.9759 33.7211 17.9415 33.7334 17.909 33.7233C15.8726 33.0859 13.7975 32.1246 11.6556 30.5408C11.6378 30.5269 11.6249 30.5048 11.6231 30.4818C11.1867 25.7778 12.076 21.0149 15.3226 16.1318C15.3304 16.1189 15.3423 16.1088 15.3561 16.1028C16.9536 15.3645 18.665 14.8213 20.4537 14.5111C20.4862 14.5061 20.5188 14.5212 20.5357 14.5502C20.7567 14.9443 21.0093 15.4496 21.1803 15.8626C23.0657 15.5726 24.9806 15.5726 26.9055 15.8626C27.0764 15.4584 27.3203 14.9443 27.5404 14.5502C27.5482 14.5358 27.5603 14.5243 27.5751 14.5173C27.5898 14.5102 27.6064 14.5081 27.6224 14.5111C29.412 14.8223 31.1234 15.3654 32.7196 16.1028C32.7337 16.1088 32.7453 16.1189 32.7522 16.1328ZM22.1388 25.0767C22.1585 23.6861 21.1518 22.5354 19.888 22.5354C18.6346 22.5354 17.6375 23.676 17.6375 25.0767C17.6375 26.4771 18.6543 27.6177 19.888 27.6177C21.1418 27.6177 22.1388 26.4771 22.1388 25.0767ZM30.4601 25.0767C30.4798 23.6861 29.4731 22.5354 28.2096 22.5354C26.9559 22.5354 25.9589 23.676 25.9589 25.0767C25.9589 26.4771 26.9756 27.6177 28.2096 27.6177C29.4731 27.6177 30.4601 26.4771 30.4601 25.0767Z"
                        fill="white"
                        fill-opacity="0.6" />
                    </svg>
                  </i>
                </a>
              </div>
              <div class="wsc-footer2020-language-pc">
                <nav class="wsc-footer2020-dropdown-toggle" aria-expanded="false">
                  <h5 class="wsc-footer2020-dropdown-title d-flex align-items-center">
                    <!-- <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g opacity="0.8" clip-path="url(#clip0_1264_3112_2)">
                      <mask id="mask0_1264_3112_2" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0"
                        width="16" height="16">
                        <path
                          d="M8 0.25C3.58 0.25 0 3.719 0 8C0 12.281 3.58 15.75 8 15.75C12.42 15.75 16 12.281 16 8C16 3.719 12.42 0.25 8 0.25ZM14.232 5H11.571C11.281 3.612 10.794 2.431 10.177 1.59C11.9267 2.14529 13.3848 3.37153 14.232 5ZM10.84 8C10.84 8.716 10.788 9.381 10.701 10H5.3C5.20699 9.3374 5.16054 8.6691 5.161 8C5.161 7.284 5.213 6.619 5.3 6H10.7C10.787 6.619 10.839 7.284 10.839 8H10.84ZM8 1.25C8.868 1.25 9.98 2.628 10.52 5H5.48C6.02 2.628 7.133 1.25 8 1.25ZM5.823 1.59C5.21 2.429 4.719 3.61 4.429 5H1.768C2.61517 3.37153 4.07335 2.14529 5.823 1.59ZM1.033 8C1.033 7.303 1.142 6.631 1.345 6H4.265C4.08417 7.3272 4.08417 8.6728 4.265 10H1.345C1.1371 9.35371 1.03149 8.6789 1.032 8H1.033ZM1.768 11H4.429C4.719 12.387 5.206 13.569 5.823 14.41C4.07335 13.8547 2.61517 12.6285 1.768 11ZM8 14.75C7.132 14.75 6.02 13.372 5.48 11H10.52C9.98 13.372 8.868 14.75 8 14.75ZM10.177 14.41C10.79 13.572 11.281 12.39 11.571 11H14.232C13.3848 12.6285 11.9267 13.8547 10.177 14.41ZM11.735 10C11.9158 8.6728 11.9158 7.3272 11.735 6H14.655C14.8628 6.64631 14.9684 7.3211 14.968 8C14.9684 8.6789 14.8628 9.35369 14.655 10H11.735Z"
                          fill="white" />
                      </mask>
                      <g mask="url(#mask0_1264_3112_2)">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M0 0H16V16H0V0Z" fill="white" />
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_1264_3112_2">
                        <rect width="16" height="16" fill="white" />
                      </clipPath>
                    </defs>
                  </svg> -->
                    <span style="margin: 0 8px">English</span>
                    <svg width="10" height="6" viewBox="0 0 10 6" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M9 1L5 5L1 1" stroke="white" stroke-linecap="round" />
                    </svg>
                  </h5>
                  <div class="wsc-footer2020-dropdown-icon">
                    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="https://www.w3.org/2000/svg">
                      <path d="M6 9L12 15L18 9" stroke="white" stroke-width="1.5"></path>
                    </svg>
                  </div>
                </nav>
                <div class="wsc-footer2020-dropdown-menu">
                  <ul>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com/">English</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.de/">Deutsch</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.fr/">Français</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.it/">Italiano</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.es/">Español</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.com.br/">Português</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.jp/">日本語</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.kr/">한국어</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link" href="https://www.wondershare.tw/">繁體中文</a></li>
                    <li class="wsc-footer2020-subnav-item"><a class="wsc-footer2020-subnav-link">简体中文</a></li>
                  </ul>
                </div>
              </div>
            </div>
            <div class="wsc-footer2020-nav">
              <div class="wsc-footer2020-brand-logo">
                <img src="https://neveragain.allstatics.com/2019/assets/icon/logo/tomovieeai-group-white.svg" alt="wondershare slogan" class="img-fluid" />
                <a class="ws_link" href="https://www.wondershare.com/" target="_blank"></a>
                <a class="tm_link" href="https://www.tomoviee.ai/"></a>
              </div>
              <div class="wsc-footer2020-social-box d-none d-xl-flex">
                <div class="d-flex align-items-center" style="gap: 4px">
                  <a href="https://x.com/tomoviee_ai" target="_blank" class="text-white">
                    <i class="wsc-icon">
                      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M31.2327 12.887H35.0747L26.6811 22.2059L36.5555 34.887H28.8227L22.767 27.196L15.8379 34.887H11.9936L20.9715 24.9193L11.5 12.887H19.4268L24.9006 19.9168L31.2304 12.887H31.2327ZM29.8843 32.6531H32.0132L18.2711 15.0035H15.9866L29.8843 32.6531Z"
                          fill="white"
                          fill-opacity="0.6" />
                      </svg>
                    </i>
                  </a>
                  <a href="https://ca.pinterest.com/ToMovieeAI/" target="_blank" class="text-white">
                    <i class="wsc-icon">
                      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M22.8257 12.1311C18.9298 12.5767 15.0476 15.8062 14.8874 20.4195C14.7867 23.2362 15.5672 25.3493 18.1835 25.9427C19.3189 23.8905 17.8173 23.4379 17.5838 21.9533C16.6247 15.8696 24.4325 11.7207 28.5184 15.9681C31.3454 18.9091 29.4844 27.9573 24.9247 27.0168C20.5572 26.1186 27.0626 18.9161 23.5765 17.5019C20.7427 16.3527 19.2365 21.0175 20.5801 23.3347C19.7927 27.3194 18.0966 31.0742 18.7833 36.0721C21.0105 34.4163 21.7613 31.2455 22.377 27.9386C23.4963 28.6351 24.0938 29.3598 25.5221 29.4724C30.7891 29.8899 33.7305 24.0852 33.0118 18.7308C32.3731 13.9839 27.7493 11.5682 22.8257 12.1311Z"
                          fill="white"
                          fill-opacity="0.6" />
                      </svg>
                    </i>
                  </a>
                  <a href="https://www.instagram.com/tomoviee.ai/" target="_blank" class="text-white">
                    <i class="wsc-icon">
                      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M20.0054 24.0614C20.0054 21.8504 21.7968 20.0576 24.0072 20.0576C26.2176 20.0576 28.0099 21.8504 28.0099 24.0614C28.0099 26.2725 26.2176 28.0653 24.0072 28.0653C21.7968 28.0653 20.0054 26.2725 20.0054 24.0614ZM17.8416 24.0614C17.8416 27.4677 20.602 30.2288 24.0072 30.2288C27.4124 30.2288 30.1728 27.4677 30.1728 24.0614C30.1728 20.6552 27.4124 17.8941 24.0072 17.8941C20.602 17.8941 17.8416 20.6552 17.8416 24.0614ZM28.976 17.6496C28.9759 17.9346 29.0603 18.2133 29.2185 18.4504C29.3767 18.6875 29.6017 18.8723 29.8649 18.9815C30.1281 19.0907 30.4178 19.1194 30.6974 19.0639C30.9769 19.0084 31.2337 18.8712 31.4353 18.6697C31.6369 18.4682 31.7742 18.2115 31.8299 17.9319C31.8856 17.6523 31.8572 17.3625 31.7482 17.0991C31.6393 16.8357 31.4547 16.6106 31.2178 16.4521C30.9809 16.2936 30.7024 16.209 30.4174 16.2089H30.4168C30.0349 16.2091 29.6686 16.3609 29.3984 16.631C29.1282 16.9011 28.9763 17.2675 28.976 17.6496ZM19.1563 33.8378C17.9856 33.7845 17.3494 33.5895 16.9265 33.4247C16.3659 33.2064 15.9659 32.9464 15.5454 32.5263C15.1249 32.1062 14.8645 31.7065 14.6473 31.1457C14.4824 30.7229 14.2874 30.0863 14.2342 28.9153C14.176 27.6493 14.1644 27.269 14.1644 24.0615C14.1644 20.8541 14.177 20.4749 14.2342 19.2078C14.2875 18.0368 14.484 17.4014 14.6473 16.9774C14.8655 16.4166 15.1254 16.0165 15.5454 15.5958C15.9654 15.1752 16.365 14.9148 16.9265 14.6975C17.3492 14.5326 17.9856 14.3375 19.1563 14.2843C20.4219 14.2261 20.8021 14.2144 24.0072 14.2144C27.2123 14.2144 27.5928 14.227 28.8596 14.2843C30.0302 14.3376 30.6654 14.5341 31.0893 14.6975C31.6499 14.9148 32.0499 15.1758 32.4705 15.5958C32.891 16.0159 33.1503 16.4166 33.3686 16.9774C33.5334 17.4001 33.7284 18.0368 33.7816 19.2078C33.8398 20.4749 33.8515 20.8541 33.8515 24.0615C33.8515 27.269 33.8398 27.6482 33.7816 28.9153C33.7283 30.0863 33.5324 30.7228 33.3686 31.1457C33.1503 31.7065 32.8904 32.1066 32.4705 32.5263C32.0505 32.946 31.6499 33.2064 31.0893 33.4247C30.6667 33.5896 30.0302 33.7846 28.8596 33.8378C27.5939 33.8961 27.2137 33.9077 24.0072 33.9077C20.8007 33.9077 20.4216 33.8961 19.1563 33.8378ZM19.0569 12.1235C17.7786 12.1817 16.9052 12.3845 16.1424 12.6814C15.3524 12.988 14.6837 13.3993 14.0154 14.0667C13.3471 14.7342 12.9369 15.4042 12.6304 16.1944C12.3336 16.9579 12.1309 17.8311 12.0727 19.1097C12.0135 20.3903 12 20.7997 12 24.0614C12 27.3232 12.0135 27.7326 12.0727 29.0132C12.1309 30.2919 12.3336 31.165 12.6304 31.9285C12.9369 32.7183 13.3472 33.389 14.0154 34.0561C14.6836 34.7233 15.3524 35.134 16.1424 35.4415C16.9066 35.7384 17.7786 35.9411 19.0569 35.9994C20.3378 36.0576 20.7464 36.0721 24.0072 36.0721C27.268 36.0721 27.6773 36.0586 28.9575 35.9994C30.2359 35.9411 31.1087 35.7384 31.872 35.4415C32.6615 35.134 33.3307 34.7236 33.999 34.0561C34.6673 33.3887 35.0766 32.7183 35.384 31.9285C35.6808 31.165 35.8844 30.2918 35.9417 29.0132C35.9999 27.7316 36.0134 27.3232 36.0134 24.0614C36.0134 20.7997 35.9999 20.3903 35.9417 19.1097C35.8835 17.831 35.6808 16.9574 35.384 16.1944C35.0766 15.4046 34.6662 14.7352 33.999 14.0667C33.3318 13.3983 32.6615 12.988 31.873 12.6814C31.1087 12.3845 30.2358 12.1808 28.9585 12.1235C27.6782 12.0653 27.2689 12.0508 24.0082 12.0508C20.7474 12.0508 20.3378 12.0643 19.0569 12.1235Z"
                          fill="white"
                          fill-opacity="0.6" />
                      </svg>
                    </i>
                  </a>
                  <a href="https://www.youtube.com/channel/UCKUHjbkaNn9z2XTqLD1ZWHw" target="_blank" class="text-white">
                    <i class="wsc-icon">
                      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M24.2402 14.4001C24.2402 14.4001 32.7979 14.3998 34.9307 14.9714C36.1101 15.29 37.0372 16.2201 37.3506 17.3982C37.9176 19.5249 37.9209 23.952 37.9209 23.9998C37.9209 23.9998 37.9207 28.4641 37.3506 30.6023C37.0329 31.7847 36.1057 32.7138 34.9307 33.0281C32.7979 33.5997 24.2402 33.6003 24.2402 33.6003C24.1809 33.6003 15.6804 33.5977 13.5508 33.0281C12.3713 32.7095 11.4442 31.7805 11.1309 30.6023C10.5607 28.4641 10.5605 23.9998 10.5605 23.9998C10.5606 23.952 10.5638 19.5249 11.1309 17.3982C11.4486 16.2156 12.3756 15.2856 13.5508 14.9714C15.6806 14.4018 24.1809 14.4002 24.2402 14.4001ZM21.5068 28.115L28.6143 23.9998L21.5068 19.8855V28.115Z"
                          fill="white"
                          fill-opacity="0.6" />
                      </svg>
                    </i>
                  </a>

                  <a href="https://www.tiktok.com/@tomoviee_ai" target="_blank" class="text-white">
                    <i class="wsc-icon">
                      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M31.5298 17.1031C31.3706 17.0208 31.2156 16.9306 31.0654 16.8328C30.6286 16.544 30.2281 16.2037 29.8726 15.8193C28.983 14.8015 28.6508 13.7689 28.5284 13.046H28.5333C28.4311 12.4459 28.4734 12.0576 28.4797 12.0576H24.4281V27.7247C24.4281 27.9351 24.4281 28.143 24.4192 28.3484C24.4192 28.374 24.4168 28.3976 24.4153 28.4251C24.4153 28.4364 24.4153 28.4482 24.4128 28.46C24.4128 28.4629 24.4128 28.4659 24.4128 28.4688C24.3701 29.031 24.1899 29.574 23.8881 30.0501C23.5863 30.5263 23.172 30.921 22.6819 31.1994C22.171 31.4901 21.5932 31.6425 21.0055 31.6418C19.1177 31.6418 17.5878 30.1025 17.5878 28.2015C17.5878 26.3005 19.1177 24.7612 21.0055 24.7612C21.3628 24.7608 21.7179 24.8171 22.0577 24.9278L22.0626 20.8024C21.0312 20.6691 19.9834 20.7511 18.9852 21.0431C17.987 21.3351 17.0602 21.8308 16.2632 22.4989C15.5649 23.1057 14.9778 23.8297 14.5284 24.6383C14.3573 24.9332 13.712 26.1181 13.6339 28.0412C13.5847 29.1328 13.9125 30.2637 14.0688 30.7311V30.7409C14.1671 31.0161 14.548 31.9553 15.1687 32.7471C15.6693 33.3822 16.2606 33.9401 16.9238 34.4029V34.393L16.9336 34.4029C18.8951 35.7357 21.0698 35.6482 21.0698 35.6482C21.4463 35.633 22.7074 35.6482 24.1396 34.9695C25.728 34.2171 26.6323 33.096 26.6323 33.096C27.21 32.4262 27.6694 31.6628 27.9907 30.8387C28.3574 29.8749 28.4797 28.719 28.4797 28.257V19.9452C28.5289 19.9747 29.1835 20.4077 29.1835 20.4077C29.1835 20.4077 30.1267 21.0122 31.5981 21.4059C32.6538 21.686 34.0761 21.745 34.0761 21.745V17.7228C33.5778 17.7769 32.5658 17.6196 31.5298 17.1031Z"
                          fill="white"
                          fill-opacity="0.6" />
                      </svg>
                    </i>
                  </a>
                  <a href="https://discord.gg/nqeukDYf7h" target="_blank" class="text-white">
                    <i class="wsc-icon">
                      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                          d="M32.7522 16.1328C35.5805 20.3207 36.9773 25.0445 36.4552 30.4828C36.453 30.5058 36.4411 30.5269 36.4223 30.5408C34.2805 32.1246 32.2053 33.0859 30.1593 33.7233C30.1433 33.7282 30.1263 33.7279 30.1105 33.7226C30.0947 33.7172 30.081 33.707 30.0713 33.6934C29.5986 33.031 29.1691 32.3327 28.7928 31.5994C28.7712 31.5562 28.791 31.5042 28.8354 31.4872C29.5175 31.2284 30.1662 30.9181 30.7901 30.5509C30.8392 30.5219 30.8423 30.4509 30.7969 30.4169C30.6645 30.3179 30.5334 30.2139 30.4078 30.1098C30.3843 30.0906 30.3527 30.0868 30.3261 30.0997C26.2757 31.9834 21.8386 31.9834 17.7402 30.0997C17.7136 30.0878 17.682 30.0919 17.6591 30.1108C17.5339 30.2148 17.4025 30.3179 17.2713 30.4169C17.2259 30.4509 17.2297 30.5219 17.2791 30.5509C17.903 30.9112 18.5516 31.2284 19.2328 31.4885C19.277 31.5055 19.2979 31.5562 19.276 31.5994C18.9079 32.3337 18.4784 33.032 17.9969 33.6943C17.9759 33.7211 17.9415 33.7334 17.909 33.7233C15.8726 33.0859 13.7975 32.1246 11.6556 30.5408C11.6378 30.5269 11.6249 30.5048 11.6231 30.4818C11.1867 25.7778 12.076 21.0149 15.3226 16.1318C15.3304 16.1189 15.3423 16.1088 15.3561 16.1028C16.9536 15.3645 18.665 14.8213 20.4537 14.5111C20.4862 14.5061 20.5188 14.5212 20.5357 14.5502C20.7567 14.9443 21.0093 15.4496 21.1803 15.8626C23.0657 15.5726 24.9806 15.5726 26.9055 15.8626C27.0764 15.4584 27.3203 14.9443 27.5404 14.5502C27.5482 14.5358 27.5603 14.5243 27.5751 14.5173C27.5898 14.5102 27.6064 14.5081 27.6224 14.5111C29.412 14.8223 31.1234 15.3654 32.7196 16.1028C32.7337 16.1088 32.7453 16.1189 32.7522 16.1328ZM22.1388 25.0767C22.1585 23.6861 21.1518 22.5354 19.888 22.5354C18.6346 22.5354 17.6375 23.676 17.6375 25.0767C17.6375 26.4771 18.6543 27.6177 19.888 27.6177C21.1418 27.6177 22.1388 26.4771 22.1388 25.0767ZM30.4601 25.0767C30.4798 23.6861 29.4731 22.5354 28.2096 22.5354C26.9559 22.5354 25.9589 23.676 25.9589 25.0767C25.9589 26.4771 26.9756 27.6177 28.2096 27.6177C29.4731 27.6177 30.4601 26.4771 30.4601 25.0767Z"
                          fill="white"
                          fill-opacity="0.6" />
                      </svg>
                    </i>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="wsc-footer2020-bottom">
        <div class="wsc-footer2020-container">
          <div class="wsc-footer2020-copyright">
            <div class="wsc-footer2020-copyright-bottom d-none d-md-block">
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/company/terms_conditions.html">Terms and Conditions</a>
              <span style="vertical-align: top">|</span>
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/privacy.html">Privacy</a>
              <span style="vertical-align: top">|</span>
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/company/terms-of-use.html" target="_blank" rel="nofollow">
                Terms of Use</a
              >
              <span style="vertical-align: top">|</span>
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/do-not-sell-or-share-my-personal-information.html"
                >Do Not Sell or Share My Personal Information</a
              >
              <span style="vertical-align: top">|</span>
              <a class="wsc-footer2020-copyright-link" id="cookie-preference-link" href="?cmpscreencustom" target="_self">Cookie Preferences</a>
            </div>
            <div class="wsc-footer2020-copyright-bottom d-block d-md-none" style="line-height: 24px">
              <div>
                <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/company/terms_conditions.html">Terms and Conditions</a>
                <span style="vertical-align: top; padding: 0rem">|</span>
                <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/privacy.html">Privacy</a>
              </div>
              <div>
                <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/company/terms-of-use.html" target="_blank" rel="nofollow">
                  Terms of Use</a
                >
                <span style="vertical-align: top; padding: 0rem">|</span>
                <a class="wsc-footer2020-copyright-link" id="cookie-preference-link" href="?cmpscreencustom">Cookie Preferences</a>
              </div>
              <a class="wsc-footer2020-copyright-link" href="https://www.wondershare.com/do-not-sell-or-share-my-personal-information.html"
                >Do Not Sell or Share My Personal Information</a
              >
            </div>
            <div class="wsc-footer2020-copyright-top">
              Copyright © <span id="copyright-year"></span>
              <script>
                document.querySelector("#copyright-year").outerHTML = new Date().getFullYear();
              </script>
              Wondershare. All rights reserved.
            </div>
          </div>
        </div>
      </div>
    </footer>
    <script src="https://neveragain.allstatics.com/2019/assets/script2021/header-footer.js"></script>
    <script src="https://neveragain.allstatics.com/2019/assets/vendor/wsc-vendor.js"></script>
    <script src="https://neveragain.allstatics.com/2019/assets/script/wsc-common.js"></script>
    <script>
      $(function () {
        wsc.common.init();
      });
    </script>
    <!-- 埋点公共方法-start -->
    <script>
      (function (w, d, s, q) {
        w[q] = w[q] || [];
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s);
        j.async = true;
        j.id = "beacon-aplus";
        j.src = "https://o.alicdn.com/QTSDK/quicktracking-sdk/qt_web.umd.js";
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "aplus_queue");

      //集成应用的appKey
      //     测试环境项目Name：PC_test_project，appkey：hsla5vyttfpkwddubjdgrmgt
      // 正式环境项目Name：Tomoviee_Web，appkey：mh7f4qf7h5kin3vy0uugb4z0
      aplus_queue.push({ action: "aplus.setMetaInfo", arguments: ["appKey", "mh7f4qf7h5kin3vy0uugb4z0"] });
      //如果是私有云部署还需要在上面那段JS后面紧接着添加日志域名埋点
      //通常私有云日志服务端域名类似于：quickaplus-web-api.xxx.com.cn, 具体域名要找交付同学要
      aplus_queue.push({ action: "aplus.setMetaInfo", arguments: ["aplus-rhost-v", "analytics.wondershare.cc:8106"] });
      // web_logs F12 查询
      // 设置 _dev_id
      // aplus_queue.push({
      //     action: 'aplus.setMetaInfo',
      //     arguments: ['_dev_id', '自定义设备ID']
      // });
      // 全局初始化uid
      function initMdUid(uid) {
        aplus_queue.push({
          action: "aplus.setMetaInfo",
          arguments: ["_user_id", uid],
        });
      }
      // 追加全局属性 params：{}
      function mdGlobal(params) {
        aplus_queue.push({
          action: "aplus.appendMetaInfo", //追加全局属性
          arguments: ["globalproperty", params],
        });
      }
      mdGlobal({ pid: 20340, did: "", session_id: "", version_code: "", plang: "en-us" });
      // 埋点曝光方法 code:埋点方案中的事件编码,params：参数{}
      function mdExp(code, params) {
        aplus_queue.push({
          action: "aplus.record",
          arguments: [code, "EXP", params],
        });
      }
      // 埋点点击 code:埋点方案中的事件编码,params：参数{}
      function mdClk(code, params) {
        aplus_queue.push({
          action: "aplus.record",
          arguments: [code, "CLK", params],
        });
      }
      aplus_queue.push({
        action: "aplus.setMetaInfo",
        arguments: ["DEBUG", true],
      });
      //当前页面url
      var page_url = window.location.href;
      //来源：上一个页面进入到这里
      var source_url = "";

      if (!sessionStorage.getItem("source_url")) {
        // 存储来源(referrer)
        sessionStorage.setItem("source_url", document.referrer);
      }
      source_url = sessionStorage.getItem("source_url");
      // 埋点公共方法--end

      // 头部点击事件上报
      $(".header_btn").on("click", () => {
        const mdClkParams = {
          page_url: page_url,
          source_url: source_url,
          button_name: "btn_start_experience_head",
        };
        mdClk("invite_code_event_promotion_click", mdClkParams);
      });
      // 中间点击事件上报
      $(".sq-btn, .content-btn ").on("click", () => {
        const mdClkParams = {
          page_url: page_url,
          source_url: source_url,
          button_name: "btn_start_experience_middle",
        };
        mdClk("invite_code_event_promotion_click", mdClkParams);
      });
    </script>
    <!--登录公测逻辑-->
    <script>
      var userInfo = {};
      function parseUrlParams(url) {
        const params = {};
        const urlArr = url.split("?");
        if (urlArr.length > 1) {
          const paramsArr = urlArr[1].split("&");
          paramsArr.forEach((item) => {
            const [key, value] = item.split("=");
            params[key] = value;
          });
        }
        return params;
      }
      var testParams = parseUrlParams(window.location.href);
      var isTest = testParams["is_test"] || false;
      window.baseConfig = {
        url: "https://effects.wondershare.com",
        domain_type: "2", //域名类型，参考https://yapi.wondershare.cn/project/356/interface/api/17346
        app_key: isTest ? "ab3d4e2b5e9f6ef2e995e34d22138b25" : "e2efebc1d5f6d8a25170707f352f5615",
        redirect: location.href, //回调地址
        pid: "20340",
        frameUrl: "",
        loadingUrl: isTest ? "https://www.tomoviee.ai/login/loading.html" : "https://www.tomoviee.ai/login/loading.html",
        timer: null,
      };

      // 防抖
      function debounce(func, delay) {
        let timeout;
        return function () {
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(this, arguments), delay);
        };
      }
      //动态计算下拉区域左边的距离，定位位置
      const dropMenu = document.querySelectorAll(
        ".wsc-header2020 .wsc-header2020-navbar-master .wsc-header2020-dropdownMenu-body .wsc-header2020-dropdownMenuBody-content"
      );
      const dropBtnGroup = document.querySelectorAll(".wsc-header2020-dropdownMenuBody-btn-group");
      const toggleElement = document.querySelector(".navbarDropdownFirst");
      function updateDropdownPosition() {
        const distanceFromViewportLeft = toggleElement.getBoundingClientRect().left;
        // dropMenu.style.marginLeft = distanceFromViewportLeft + 'px';
        dropMenu.forEach((item) => {
          item.style.marginLeft = distanceFromViewportLeft + "px";
        });
        dropBtnGroup.forEach((item2) => {
          item2.style.marginLeft = distanceFromViewportLeft + "px";
        });
      }
      //初始化执行一次
      window.addEventListener("DOMContentLoaded", function () {
        // updateDropdownPosition();
        setTimeout(() => {
          // updateDropdownPosition();
        }, 2000);
      });
      // 监听窗口变化
      // window.addEventListener('resize', debounce(updateDropdownPosition, 100));

      // hover显示和折叠
      $(".wsc-header2020-navbar-dropdown")
        .on("mouseenter", function () {
          $(this).find(".wsc-header2020-navbarDropdown-toggle").attr("aria-expanded", "true");
          dropdown.style.display = "none";
        })
        .on("mouseleave", function () {
          $(this).find(".wsc-header2020-navbarDropdown-toggle").attr("aria-expanded", "false");
        });

      // 有登录头像的时候
      const dropdown = document.querySelector(".ws-user-panel-dropdown");
      const avatar = document.querySelector(".wondershare-user-panel.log-in .avatar");
      function toggleDropdown() {
        const isHidden = getComputedStyle(dropdown).display === "none";
        dropdown.style.display = isHidden ? "block" : "none";
        document.querySelector(".navbarDropdownFirst").setAttribute("aria-expanded", false);
      }
      if (avatar) {
        avatar.addEventListener("click", function (event) {
          event.stopPropagation(); // Prevent this click from reaching document
          toggleDropdown();
        });
      }
      document.addEventListener("click", function (event) {
        if (!dropdown.contains(event.target) && event.target !== avatar) {
          dropdown.style.display = "none";
        }
      });
      if (dropdown) {
        dropdown.addEventListener("click", function (event) {
          event.stopPropagation();
        });
      }

      // 点击其他区域关闭弹窗
      document.addEventListener("click", function (event) {
        const wsheader = document.querySelector(".wsc-header2020");
        const dropdownToggles = document.querySelectorAll(".wsc-header2020-navbarDropdown-toggle");
        const dropdownMenus = document.querySelectorAll(".wsc-header2020-navbarDropdown-menu");
        // // 检查点击是否在 `wsheader` 内部
        const isClickInsideHeader = wsheader.contains(event.target);
        // // 如果点击在外部，关闭所有下拉菜单
        if (!isClickInsideHeader) {
          dropdownToggles.forEach((toggle) => {
            toggle.setAttribute("aria-expanded", "false");
          });
        }
      });
      // 有信息后，默认加载信息
      function dealMessage() {
        var el_log_out = document.querySelector(".wondershare-user-panel.log-out"),
          el_log_in = document.querySelector(".wondershare-user-panel.log-in"),
          el_dropdown = document.querySelector(".ws-user-panel-dropdown"),
          img_avatar = document.querySelector(".wondershare-user-panel.log-in .avatar"),
          requset_url = document.querySelector(".wondershare-user-panel.log-out [data-href]").getAttribute("data-href").replace(/\/$/, "");
        el_log_out.style.display = "none";
        el_log_in.style.display = "block";
        userInfo.avatar && img_avatar.setAttribute("src", userInfo.avatar);

        document.querySelector(".ws-user-panel-dropdown .account_name").innerText =
          userInfo.first_name && userInfo.last_name ? userInfo.first_name + " " + userInfo.last_name : "" || userInfo.nickName || userInfo.email;

        document.querySelector(".ws-user-panel-dropdown .account_url").setAttribute("href", requset_url);
      }
      //探测能不能第三方跨域
      cookieCheckCall();
      function cookieCheckCall() {
        const frame = document.createElement("iframe");
        frame.id = "3pc";
        frame.src = `https://accounts.wondershare.com/web/cookie-check`;
        frame.style.display = "none";
        frame.style.position = "fixed";
        function setResult(rs) {
          window.cookie_check_supported = rs;
          setCookie("cookie_check_supported ", rs + "", 1440);
          document.body.removeChild(frame);
          window.removeEventListener("message", listen);
        }
        function listen(event) {
          if (["3pc.supported", "3pc.unsupported"].includes(event.data)) {
            setResult(event.data === "3pc.supported");
          }
        }
        frame.onerror = () => setResult(false);
        window.addEventListener("message", listen, false);
        document.body.appendChild(frame);
      }
      document.querySelectorAll(".wondershare-user-panel.log-out a").forEach((el) => {
        el.addEventListener("click", function (e) {
          e.preventDefault();
          loadFrame(true);
        });
      });
      // 默认弹窗加载
      function loadFrame(open) {
        var href = `https://accounts.wondershare.com/v3/user/oauth-client/authorize?app_key=${window.baseConfig.app_key}&redirect_uri=${encodeURIComponent(
          window.baseConfig.loadingUrl
        )}&response_type=code&source=109&scope=${encodeURIComponent("user normal")}&product_id=${window.baseConfig.pid}&lang=en-us&mode=1`;
        $("#login_frame_container iframe").hide();
        $("#login_frame_container iframe").attr("src", href);
        $("#login_frame_container iframe").on("load", function (e) {
          $("#login_frame_container iframe").show();
          $("#login_frame_container iframe+div").hide();
        });
        if (window.cookie_check_supported) {
          open && $("#ws_login_modal").modal("show");
        } else {
          window.open(href, "ToMoviee", "width=600,height=680,left=500,top=300");
        }
      }
      window.addEventListener("message", (event) => {
        // 验证来源以确保安全
        // if (event.origin !== 'https://你的登录域名') return;
        if (event.data.type === "RESPONSE") {
          // 处理登录成功逻辑
          console.log("收到登录信息:", event.data.result.answer);
          getUserInfo(event.data.result.answer.is_new_user);
        }
      });

      function getCookie(name) {
        var v = document.cookie.match("(^|;) ?" + name + "=([^;]*)(;|$)");
        return v ? decodeURIComponent(v[2]) : null;
      }
      function setCookie(name, value, minutes) {
        var expires = "";
        if (minutes) {
          var date = new Date();
          date.setTime(date.getTime() + minutes * 60 * 1000);
          expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/; domain=.tomoviee.ai";
      }

      const childFrame = document.getElementById("login_frame");
      var requestId = 0;
      var pendingRequests = {};
      // 等待iframe加载完成
      childFrame.onload = function () {
        // 监听子页面发来的消息
        window.addEventListener("message", function (event) {
          // 安全检查：验证来源
          // if (event.origin !== "xxxx") return;
          var data = event.data;
          // 处理响应消息
          if (data.type === "RESPONSE") {
            var request = pendingRequests[data.requestId];
            if (request) {
              // 调用成功回调
              request.success(data.result);
              delete pendingRequests[data.requestId];
            }
          }
          // 处理错误消息
          else if (data.type === "ERROR") {
            var request = pendingRequests[data.requestId];
            if (request) {
              // 调用错误回调
              request.error(data.error);
              delete pendingRequests[data.requestId];
            }
          }
        });
        // 示例：向子页面发送请求(AJAX风格)
        askChildForData(
          { question: "give me a token" },
          // 成功回调
          function (result) {
            console.log("从子页面获得的结果:", result);
            if (result.answer.access_token) {
              // console.log("答案是：", result.answer);
              getUserInfo(result.answer.is_new_user);
              $("#ws_login_modal").modal("hide");
            }
          },
          // 失败回调
          function (error) {
            console.error("请求失败:", error);
          }
        );
      };
      $(".account_url_sign_out").on("click", function () {
        logout();
      });
      // 登出
      function logout() {
        $.ajax({
          type: "GET",
          url: "https://app.tomoviee.ai/web/v1/logout",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          data: {},
          xhrFields: {
            withCredentials: true,
          },
          crossDomain: true,
          success: function (res) {
            window.location.href = "https://accounts.wondershare.com/v3/user/oauth/logout?redirect=" + (window.ws_redirect_url_logout || location.href);
          },
          error: function (err) {
            console.error(err);
          },
        });
      }
      // ip_btn
      function ipBtn() {
        setCookie("ipTime", "oneDay", 1440);
        $("#ip_sure_modal").modal("hide");
      }
      getIp();
      function getIp() {
        $.ajax({
          type: "GET",
          url: "https://app.tomoviee.ai/web/v1/user/ip",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          data: {},
          xhrFields: {
            withCredentials: true,
          },
          crossDomain: true,
          success: function (res) {
            console.log("ip判别", res.data);
            if (res.code == 0) {
              const arr = window.location.hostname.split(".");
              console.log(arr);
              const pageIsCn = arr[arr.length - 1] === "cn";
              const ipIsCn = res.data.user_ip_region === "CN";
              if (getCookie("ipTime") !== "oneDay") {
                if (pageIsCn !== ipIsCn) {
                  $("#ip_sure_modal").modal("show");
                }
              }
            }
          },
          error: function (err) {
            console.error("66:", err);
          },
        });
      }
      // 记录点了什么
      var click_what = "";
      // 0:没有填写问卷；1：填写问卷但是问卷审核中；2：填写过问卷且审核通过
      var wjStatus = 1;
      // 公测状态
      function getStatus() {
        $.ajax({
          type: "GET",
          url: "https://app.tomoviee.ai/web/v1/op/beta-questionnaire/check",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          data: {},
          xhrFields: {
            withCredentials: true,
          },
          crossDomain: true,
          success: function (res) {
            // console.log("公测状态:", res.data);
            if (res.code == 0) {
              wjStatus = res.data.status;
              changeBtnStatus(wjStatus);
              if (res.data.invite_code) {
                $("#yqm").val(res.data.invite_code);
                $(".code_title").text("We've detected you already have an approved invitation code");
                $("#invite_code_modal").modal("show");
                const mdParams = {
                  source_url: source_url,
                  popup_name: "invite_pop",
                };
                mdExp("invite_code_event_promotion_visit", mdParams);
              }
              if (click_what === "need_apply" && !userInfo.invite_passed && wjStatus === 0) {
                dataLayer.push({
                  event: "conditional_redirect",
                  redirect_url: "https://www.tomoviee.ai/survey.html",
                });
                window.location.href = "https://www.tomoviee.ai/survey.html";
              }
              if (click_what === "invite_code" && !userInfo.invite_passed) {
                $("#invite_code_modal").modal("show");
                const mdParams = {
                  source_url: source_url,
                  popup_name: "invite_pop",
                };
                mdExp("invite_code_event_promotion_visit", mdParams);
              }
            }
          },
          error: function (err) {
            console.error("公测:", err);
          },
        });
      }
      //改变按钮状态
      function changeBtnStatus(status) {
        if (!userInfo.invite_passed) {
          $(".status_true").hide();
          $(".gc_box").show();
          if (status == 1) {
            $(".status_text").text("Priority approval underway!");
            $(".status_ing").show();
            $(".txyqm-btn-text").hide();
            $(".txyqm-btn").show();
            $(".sq-btn-top").hide();
            $(".btn-text").each(function (index, el) {
              $(el).text("Enter invitation code");
            });
          }
          if (status == 2) {
            $(".status_text").text("Your application has been approved");
            $(".status_pass").show();
            $(".txyqm-btn-text").hide();
            $(".txyqm-btn").show();
            $(".sq-btn-top").hide();
            $(".btn-text").each(function (index, el) {
              $(el).text("Enter invitation code");
            });
          }
        } else {
          $(".status_true").show();
          $(".gc_box").hide();
          $(".btn-text").each(function (index, el) {
            $(el).text("Join the Beta");
          });
        }
      }

      // 监听所有按钮
      $(".sq-btn, .content-btn ,.header_btn,.bot_mobile_video,.bot_video").on("click", handleButtonClick);
      function handleButtonClick() {
        if (userInfo.uid) {
          if (!userInfo.invite_passed) {
            if (wjStatus == 1 || wjStatus == 2) {
              $("#invite_code_modal").modal("show");
              const mdParams = {
                page_url: page_url,
                source_url: source_url,
                popup_name: "invite_pop",
              };
              mdExp("invite_code_event_promotion_visit", mdParams);
            }
            if (wjStatus == 0) {
              // 前往申请
              const mdClkParams = {
                page_url: page_url,
                source_url: source_url,
                button_name: "btn_apply_now",
              };
              mdClk("invite_code_event_promotion_click", mdClkParams);
              dataLayer.push({
                event: "conditional_redirect",
                redirect_url: "https://www.tomoviee.ai/survey.html",
              });
              window.location.href = "https://www.tomoviee.ai/survey.html";
            }
          }
          if (userInfo.invite_passed) {
            // 立即创作
            const mdClkParams = {
              page_url: page_url,
              source_url: source_url,
              button_name: "btn_create_now",
            };
            mdClk("invite_code_event_promotion_click", mdClkParams);
            dataLayer.push({
              event: "conditional_redirect",
              redirect_url: "https://app.tomoviee.ai/",
            });
            window.location.href = "https://app.tomoviee.ai/";
          }
        } else {
          // 前往申请
          const mdClkParams = {
            page_url: page_url,
            source_url: source_url,
            button_name: "btn_apply_now",
          };
          mdClk("invite_code_event_promotion_click", mdClkParams);
          click_what = "need_apply";
          loadFrame(true);
        }
      }
      getUserInfo();
      function getUserInfo(isNew) {
        $.ajax({
          type: "GET",
          url: "https://app.tomoviee.ai/web/v1/user-info",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          data: {},
          xhrFields: {
            withCredentials: true,
          },
          crossDomain: true,
          success: function (res) {
            if (res.code == 0) {
              userInfo = res.data;
              dealMessage();
              getStatus();
              if (userInfo.invite_passed) {
                $(".bot_mobile_video").attr("data-src", "https://www.tomoviee.ai/videos/home/<USER>");
                $(".bot_video").attr("data-src", "https://www.tomoviee.ai/videos/home/<USER>");
              }
              initMdUid(String(userInfo.uid));
              mdGlobal({ uid: String(userInfo.uid) });
              //如果是新用户那就需要报一下注册
              isNew && mdClk("user_signin_result", { source_url: source_url, access_url: page_url });
            }
          },
          error: function (err) {
            console.error("用户信息请求失败:", err);
          },
        });
      }
      // 向子页面发送请求的函数(AJAX风格)
      function askChildForData(payload, successCallback, errorCallback) {
        var currentId = ++requestId;
        // 存储请求以便后续处理响应
        pendingRequests[currentId] = {
          success: successCallback,
          error: errorCallback || function () {}, // 默认空错误处理
        };
        // 发送消息给子页面
        childFrame.contentWindow.postMessage({
          type: "REQUEST",
          requestId: currentId,
          payload: payload,
        });
        // 设置超时
        setTimeout(function () {
          if (pendingRequests[currentId]) {
            var request = pendingRequests[currentId];
            delete pendingRequests[currentId];
            request.error("请求超时");
          }
        }, 10000); // 5秒超时
      }
    </script>
    <script type="text/javascript" src="//script.crazyegg.com/pages/scripts/0104/8432.js" async="async"></script>
    <script src="https://www.wondershare.com/assets/js/swiper-bundle7.min.js"></script>
    <script src="https://www.wondershare.com/assets/aos/aos.min.js"></script>
    <script src="https://www.tomoviee.ai/riv/rive.js"></script>
    <script>
      const r = new rive.Rive({
        src: "https://www.tomoviee.ai/riv/tomoviee_home.riv",
        canvas: document.getElementById("canvas"),
        autoplay: true,
        stateMachines: "State Machine 1",
        artboard: "Main",
        onLoad: () => {
          r.resizeDrawingSurfaceToCanvas();
        },
      });
    </script>
    <script>
      function getDeviceOS() {
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;

        // 检测 Android
        if (/android/i.test(userAgent)) {
          return "android";
        }

        // 检测 iOS（iPhone/iPad/iPod）
        if (/iPad|iPhone|iPod/i.test(userAgent) || (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1)) {
          return "ios";
        }

        // 其他设备（如 Windows、Mac 非触控设备等）
        return "other";
      }
      var mobile_sys = getDeviceOS();
      if (mobile_sys === "ios" && window.innerWidth <= 768) {
        $(".tianmu-top").css("height", "calc(95vh - 48px)");
      }
    </script>
    <script>
      // document.addEventListener('DOMContentLoaded', function () {
      //   const video = document.querySelector('.bot_video');
      //   const animatedContent = document.querySelector('.animated-content');
      //   // 懒加载视频
      //   if ('IntersectionObserver' in window) {
      //     const lazyVideoObserver = new IntersectionObserver((entries) => {
      //       entries.forEach((entry) => {
      //         if (entry.isIntersecting) {
      //           const video = entry.target;
      //           video.src = video.dataset.src;
      //           video.load();
      //           lazyVideoObserver.unobserve(video);

      //           // 视频加载后处理播放逻辑
      //           handleVideoPlayback(video);
      //         }
      //       });
      //     }, { threshold: 0.1 });
      //     lazyVideoObserver.observe(video);
      //   } else {
      //     // 不支持 IntersectionObserver 的备用方案
      //     video.src = video.dataset.src;
      //     video.load();
      //     handleVideoPlayback(video);
      //   }

      //   // 处理视频播放逻辑
      //   function handleVideoPlayback(video) {
      //     // 确保视频可以播放
      //     video.addEventListener('canplay', () => {
      //       // 尝试自动播放（可能被浏览器阻止）
      //       const playPromise = video.play();
      //       if (playPromise !== undefined) {
      //         playPromise.catch(error => {
      //           console.log('自动播放被阻止，需要用户交互');
      //           // 可以在这里添加一个播放按钮让用户手动触发
      //         });
      //       }
      //     });

      //     // 监听视频结束事件（临时取消 loop，播放完再触发动画）
      //     video.loop = false; // 先取消循环
      //     video.addEventListener('ended', () => {
      //       // 显示动画
      //       animatedContent.classList.add('show');
      //       // 如果需要重新循环播放视频
      //       video.currentTime = 0;
      //       video.loop = true;
      //       video.play();
      //     });
      //   }
      // });
    </script>
    <script>
      // 0:没有填写问卷；1：填写问卷但是问卷审核中；2：填写过问卷且审核通过
      // var wjStatus = 0
      // $("#invite_code_modal").modal("show");
      // 邀请码申请页面曝光事件-首页曝光一次

      mdExp("invite_code_event_promotion_visit", { source_url: source_url, popup_name: "", page_url: page_url });
      function callCodeModal() {
        if (userInfo.uid) {
          $("#invite_code_modal").modal("show");
          const mdParams = {
            page_url: page_url,
            source_url: source_url,
            popup_name: "invite_pop",
          };
          mdExp("invite_code_event_promotion_visit", mdParams);
          const mdClkParams = {
            source_url: source_url,
            button_name: "btn_fill_invite_code",
          };
          mdClk("invite_code_event_promotion_click", mdClkParams);
        } else {
          const mdClkParams = {
            source_url: source_url,
            button_name: "btn_fill_invite_code",
          };
          mdClk("invite_code_event_promotion_click", mdClkParams);
          click_what = "invite_code";
          loadFrame(true);
        }
      }
      // callCodeModal()
      document.querySelectorAll("input").forEach((input) => {
        input.setAttribute("autocomplete", "off");
      });
      document.addEventListener("DOMContentLoaded", function () {
        $("#invite_code_modal").on("hidden.bs.modal", function () {
          // 清除输入框内容
          $("#yqm").val("");
          // 清除错误提示
          $(".invalid-feedback").text("");
          const mdClkParams = {
            source_url: source_url,
            button_name: "btn_close",
          };
          mdClk("invite_code_event_promotion_click", mdClkParams);
        });
      });
      $(".inv-btn").click(function (e) {
        e.preventDefault();
        const mdClkParams = {
          source_url: source_url,
          button_name: "btn_submit",
        };
        mdClk("invite_code_event_promotion_click", mdClkParams);
        const inputValue = $("#yqm").val();
        if (!inputValue) {
          // alert('Please enter your invitation code！');
          $(".invalid-feedback").text("Please enter your invitation code！");
        } else {
          console.log("输入的邀请码:", inputValue);
          submitCode(inputValue);
          $(".invalid-feedback").text("");
        }
        // 这里可以发送 AJAX 请求或进行其他操作
      });
      // 提交验证码
      function submitCode(val) {
        $.ajax({
          type: "POST",
          url: "https://app.tomoviee.ai/web/v1/op/invite-code/verify",
          headers: {
            "Content-Type": "application/json; charset=utf-8",
          },
          data: JSON.stringify({
            invite_code: val,
          }),
          xhrFields: {
            withCredentials: true,
          },
          crossDomain: true,
          success: function (res) {
            if (res.code == 0) {
              dataLayer.push({
                event: "conditional_redirect",
                redirect_url: "https://app.tomoviee.ai/?isNew=1",
              });
              window.location.href = "https://app.tomoviee.ai/?isNew=1";
              const mdClkParams = {
                source_url: source_url,
                referral_code_source: res.data.source_type,
                referral_code: val,
                task_result: "1",
              };
              mdClk("invite_code_event_promotion_task", mdClkParams);
            }
          },
          error: function (err) {
            $(".invalid-feedback").text("Invitation code invalid. Please verify and re-enter.");
            const mdClkParams = {
              source_url: source_url,
              referral_code: val,
              task_result: "0",
            };
            mdClk("invite_code_event_promotion_task", mdClkParams);
          },
        });
      }
    </script>
    <script>
      $(function () {
        var audioOk = false;
        $(".audio-no").click(function () {
          if (!audioOk) {
            $(".advantagesSwiper .swiper-slide-active video")[0].play();
            $(".advantagesSwiper .swiper-slide-active video")[0].muted = false;
            audioOk = true;
            $(".audio-no").addClass("audio-yes");
          } else {
            $(".advantagesSwiper .swiper-slide-active video")[0].muted = true;
            audioOk = false;
            $(".audio-no").removeClass("audio-yes");
          }
        });

        // 点击swiper切换方法
        function checkSwiper(swiper, thumbsAll, index) {
          thumbsAll.removeClass("active");
          thumbsAll.eq(index).addClass("active");
          swiper.slideTo(index, 500, false);
        }
        var swiper_inited_new = false;
        if (document.body.clientWidth > 1280) {
          var scrollElement = document.scrollingElement || document.documentElement || document.body;
          var advantagesSwiper = new Swiper(".advantagesSwiper", {
            slidesPerView: 1,
            noSwiping: true,
            initialSlide: 1, //默认第几个在中心位置显示，总数量中间的一个，
            slidesPerView: "auto",
            speed: 800,
            effect: "creative",
            loop: true,
            centeredSlides: true,
            slideToClickedSlide: false, // 允许点击切换
            // grabCursor: true,
            creativeEffect: {
              prev: {
                translate: [0, 0, 0], // 偏移量
                scale: 0.9, // 缩放量
                shadow: true, // 是否加阴影
              },
              next: {
                translate: [58, 0, 0],
                scale: 0.9,
                shadow: true,
              },
              limitProgress: 3, // 显示4个堆叠的最重要的这个属性，后面依次以前面属性等比配置
              shadowPerProgress: true, //是否等比配置透明度
              perspective: true,
            },
            pagination: {
              el: ".advantagesSwiper-pagination",
              clickable: false,
            },
            on: {
              init() {
                // $('.advantagesSwiper video')[this.activeIndex].play()
                swiper_inited_new = true;
              },

              slideChange: function () {
                if (!$(".advantagesSwiper video").eq(this.activeIndex).attr("src") && swiper_inited_new) {
                  var videoSrc = $(".advantagesSwiper video").eq(this.activeIndex).attr("data-src");
                  $(".advantagesSwiper video").eq(this.activeIndex).attr("src", videoSrc);
                }
                if (swiper_inited_new) {
                  // console.log('newvideo', $('.advantagesSwiper video'));
                  $(".advantagesSwiper video")[this.activeIndex].play();
                  console.log(audioOk);
                  if (audioOk) {
                    $(".advantagesSwiper video")[this.activeIndex].muted = true;
                    $(".audio-no").removeClass("audio-yes");
                    audioOk = false;
                  }
                  $(".advantagesSwiper video")[this.previousIndex].pause();
                  $(".advantagesSwiper video")[this.previousIndex].muted = true;
                }
              },
              slideChangeTransitionStart: function () {},
            },
          });
          //模拟滚动
          function loop(previousScrollTop, callback) {
            function repeat() {
              requestAnimationFrame(function () {
                loop(previousScrollTop, callback);
              });
            }
            var scrollTop = scrollElement.scrollTop;
            if (previousScrollTop === scrollTop) {
              return repeat();
            } else {
              callback && callback(previousScrollTop, scrollTop);
              previousScrollTop = scrollTop;
            }
            return repeat();
          }

          loop(0, function (previousScrollTop, scrollTop) {
            $(".part-advantages .list-item").each(function (advantageIndex, advantageEl) {
              var advantageTop = advantageEl.getBoundingClientRect().top;
              var activeIndex = 0;
              if (advantageIndex == 0 && advantageTop > ($(advantageEl).innerHeight() / 3) * -1) {
                activeIndex = 0;
                checkSwiper(advantagesSwiper, $(".part-advantages .list-item"), activeIndex);
              } else if (advantageTop < ($(advantageEl).innerHeight() / 4) * -3 && Math.abs(advantageTop) < $(advantageEl).innerHeight()) {
                activeIndex = advantageIndex + 1;
                if (activeIndex >= $(".part-advantages .list-item").length) {
                  activeIndex = $(".part-advantages .list-item").length - 1;
                }
                checkSwiper(advantagesSwiper, $(".part-advantages .list-item"), activeIndex);
              }
            });
          });
        }
      });
    </script>
    <script>
      $(function () {
        function debounce(func, delay) {
          let timer;
          return function () {
            const context = this;
            const args = arguments;
            clearTimeout(timer);
            timer = setTimeout(() => {
              func.apply(context, args);
            }, delay);
          };
        }
        function myFunction() {
          AOS.refresh();
        }
        const debouncedFunction = debounce(myFunction, 300);
        AOS.init({
          mirror: true, // 默认 true，确保启用
          once: false, // 必须为 false，否则动画只触发一次
        });

        // var audioOk = false
        // 微信自动播放
        document.addEventListener(
          "WeixinJSBridgeReady",
          () => {
            const v = document.querySelector(".banner-video .embed-responsive-custom video");
            v.play();
            v.muted = true;
            var lazy_videos_ws = [];
            document.querySelectorAll(".lazy-video").forEach(function (el) {
              lazy_videos_ws.push(el);
            });

            window.addEventListener("scroll", function () {
              lazy_videos_ws &&
                lazy_videos_ws.forEach((el, index) => {
                  var top = el.getBoundingClientRect().top;
                  if (top <= window.innerHeight * 2) {
                    el.src = el.getAttribute("data-src");
                    el.setAttribute("webkit-playsinline", "true");
                    el.setAttribute("playsinline", "true");
                    lazy_videos_ws.splice(index, 1);
                    lazy_videos_ws = lazy_videos_ws.length === 0 ? null : lazy_videos_ws;
                    el.play();
                    el.muted = true;
                  }
                });
            });
          },
          false
        );

        // 视频滚动加载
        // var lazy_videos = []
        // document.querySelectorAll('.lazy-video').forEach(function (el) {
        //   lazy_videos.push(el)
        // })
        // var createTop = $('.create-box').offset().top,
        //   dmxTop = $('.dmx-box').offset().top,
        //   allHeight = $(window).height()
        // window.addEventListener('scroll', function () {
        //   var srcollTop = $(window).scrollTop()
        //   lazy_videos && lazy_videos.forEach(function (el, index) {
        //     var top = el.getBoundingClientRect().top
        //     if (top <= window.innerHeight * 2) {
        //       el.src = el.getAttribute('data-src')
        //       el.setAttribute('webkit-playsinline', '')
        //       el.setAttribute('playsinline', 'true')
        //       lazy_videos.splice(index, 1)
        //       lazy_videos = lazy_videos.length === 0 ? null : lazy_videos
        //     }
        //   })
        // if (srcollTop >= createTop || srcollTop <= dmxTop - allHeight) {
        // if (audioOk) {
        //   $('.process-container .process-slide.active video')[0].muted = true
        //   audioOk = false
        //   $('.audio-no').removeClass('audio-yes')
        // }
        // }
        // })
        // 视频滚动到视口位置才加载
        var lazy_videos = [];
        document.querySelectorAll(".lazy-video").forEach(function (el) {
          lazy_videos.push(el);
        });
        window.addEventListener("scroll", function () {
          lazy_videos &&
            lazy_videos.forEach(function (el, index) {
              if (isElementInViewport(el)) {
                el.src = el.getAttribute("data-src");
                if (el.getAttribute("data-poster")) {
                  el.poster = el.getAttribute("data-poster");
                }
                el.setAttribute("webkit-playsinline", "true");
                el.setAttribute("playsinline", "true");
                lazy_videos.splice(index, 1);
                lazy_videos = lazy_videos.length === 0 ? null : lazy_videos;
              }
            });
        });
        function isElementInViewport(el) {
          var rect = el.getBoundingClientRect();
          return rect.top >= 0 && rect.bottom <= (window.innerHeight || document.documentElement.clientHeight);
        }
        // 点击swiper切换方法
        function checkSwiper(swiper, thumbsAll, index) {
          thumbsAll.removeClass("active");
          thumbsAll.eq(index).addClass("active");
          swiper.slideTo(index, 500, false);
        }
        // 创建公共image swiper方法
        function createCommonImageSwiper(elm, thumbsArray) {
          return new Swiper(elm, {
            slidesPerView: 1,
            spaceBetween: 15,
            loop: true,
            autoplay: {
              delay: 3000,
              disableOnInteraction: false,
            },
            pagination: {
              el: elm + "-pagination",
            },
            on: {
              slideChangeTransitionStart: function () {
                // thumbsArray.removeClass('active')
                // thumbsArray.eq(this.activeIndex).addClass('active')
              },
            },
          });
        }

        var audioOkMobile = false;
        $(".audio-no-mobile").click(function () {
          if (!audioOkMobile) {
            $(".video-swiper .swiper-slide-active video")[0].play();
            $(".video-swiper .swiper-slide-active video")[0].muted = false;
            audioOkMobile = true;
            $(".audio-no-mobile").addClass("audio-yes-mobile");
          } else {
            $(".video-swiper .swiper-slide-active video")[0].muted = true;
            audioOkMobile = false;
            $(".audio-no-mobile").removeClass("audio-yes-mobile");
          }
        });
        var video_swiper = createCommonVideoSwiperVoice(".video-swiper");
        // 创建公共video vioce swiper方法 有声音
        function createCommonVideoSwiperVoice(elm, thumbsArray) {
          var swiper_inited_mobile = false;
          document.querySelectorAll(elm + " video").forEach(function (el) {
            el.removeAttribute("autoplay");
          });
          return new Swiper(elm, {
            slidesPerView: 1,
            spaceBetween: 30,
            noSwiping: true,
            loop: true,
            breakpoints: {
              992: {
                spaceBetween: 15,
                noSwiping: true,
              },
            },
            pagination: {
              el: elm + "-pagination",
            },

            on: {
              init() {
                var swiper = this;
                swiper_inited_mobile = true;
                $(elm + " video").each(function (index, el) {
                  el.addEventListener("ended", function () {
                    swiper.isEnd ? swiper.slideTo(0) : swiper.slideNext();
                  });
                });
                $(elm + " video")[this.activeIndex].play();
              },
              slideChange: function () {
                if (swiper_inited_mobile) {
                  $(elm + " video")[this.activeIndex].play();
                  if (audioOkMobile) {
                    $(elm + " video")[this.activeIndex].muted = true;
                    $(".audio-no-mobile").removeClass("audio-yes-mobile");
                    audioOkMobile = false;
                  }
                  $(elm + " video")[this.previousIndex].pause();
                  $(elm + " video")[this.previousIndex].muted = true;
                }
              },
              slideChangeTransitionStart: function () {
                // thumbsArray.removeClass('active')
                // thumbsArray.eq(this.activeIndex).addClass('active')
              },
            },
          });
        }
        // 创建公共video swiper方法
        function createCommonVideoSwiper(elm, thumbsArray) {
          var swiper_inited = false;
          document.querySelectorAll(elm + " video").forEach(function (el) {
            el.removeAttribute("autoplay");
          });
          return new Swiper(elm, {
            slidesPerView: 1,
            spaceBetween: 30,
            noSwiping: true,
            loop: true,
            breakpoints: {
              992: {
                spaceBetween: 15,
                noSwiping: true,
              },
            },
            pagination: {
              el: elm + "-pagination",
            },

            on: {
              init() {
                var swiper = this;
                swiper_inited = true;
                $(elm + " video").each(function (index, el) {
                  el.addEventListener("ended", function () {
                    swiper.isEnd ? swiper.slideTo(0) : swiper.slideNext();
                  });
                });
                $(elm + " video")[this.activeIndex].play();
              },
              slideChange: function () {
                swiper_inited && ($(elm + " video")[this.activeIndex].play(), $(elm + " video")[this.previousIndex].pause());
              },
              slideChangeTransitionStart: function () {
                // thumbsArray.removeClass('active')
                // thumbsArray.eq(this.activeIndex).addClass('active')
              },
            },
          });
        }

        var video_swiper_s5 = createCommonVideoSwiper(".video-swiper-s5");
        var img_swiper_s7 = createCommonImageSwiper(".img-swiper-s7");
        var img_swiper_s8 = createCommonImageSwiper(".img-swiper-s8");
        const swiperEl = document.querySelector("#tab-swiper1 .swiper-wrapper");
        swiperEl.style.transitionTimingFunction = "cubic-bezier(0.42, 0, 1, 1)";
        var swiper_inited_two = false;
        var videoSwiper = new Swiper("#tab-swiper1", {
          slidesPerView: "auto",
          spaceBetween: 30,
          centeredSlides: true,
          loop: true,
          speed: 600,
          // 允许同时支持点击和触摸滚动
          // 增强触摸和点击配置
          simulateTouch: true, // 启用触摸模拟
          touchRatio: 1, // 触摸事件的比例
          shortSwipes: true, // 允许短距离滑动
          longSwipes: true, // 允许长距离滑动
          followFinger: true, // 滑动时跟随手指
          // 关键修改 - 提高点击灵敏度
          touchEventsTarget: "swiper-container", // 监听整个容器
          threshold: 30, // 滑动阈值降低
          slideToClickedSlide: true, // 允许点击切换
          // 防止事件冲突
          preventClicks: false,
          preventClicksPropagation: false,
          breakpoints: {
            992: {
              spaceBetween: 24,
            },
          },
          navigation: {
            nextEl: ".swiper-navigation-box .swiper-button-next",
            prevEl: ".swiper-navigation-box .swiper-button-prev",
          },
          pagination: {
            el: ".swiperTab-pagination",
            clickable: true,
          },
          on: {
            init() {
              var swiper = this;
              swiper_inited_two = true;
              $("#tab-swiper1 video").each(function (index, el) {
                el.addEventListener("ended", function () {
                  swiper.isEnd ? swiper.slideTo(0) : swiper.slideNext();
                });
              });
              $("#tab-swiper1 video")[this.activeIndex].play();
            },
            slideChange: function () {
              if (!$("#tab-swiper1 video").eq(this.activeIndex).attr("src") && swiper_inited_two) {
                var videoSrc = $("#tab-swiper1 video").eq(this.activeIndex).attr("data-src");
                $("#tab-swiper1 video").eq(this.activeIndex).attr("src", videoSrc);
              }
              if (swiper_inited_two) {
                $("#tab-swiper1 video")[this.activeIndex].play();
                $("#tab-swiper1 video")[this.previousIndex].pause();
              }
            },
            slideChangeTransitionStart: function () {
              $(".tab-intro").removeClass("active");
              $(".tab-intro").eq(this.realIndex).addClass("active");
              $(".tab-intro-mobile").removeClass("active");
              $(".tab-intro-mobile").eq(this.realIndex).addClass("active");
            },
          },
        });
        $(".tab-intro").on("click", function () {
          var index = $(this).index();
          $(this).addClass("active").siblings().removeClass("active");

          videoSwiper.slideToLoop(index, 500, false);
        });
        $(".tab-intro-mobile").on("click", function () {
          var index = $(this).index();
          $(this).addClass("active").siblings().removeClass("active");
          videoSwiper.slideToLoop(index, 500, false);
        });
      });
    </script>
    <script>
      window.addEventListener("load", function () {
        setTimeout(function () {
          var a = window.performance.getEntriesByType("navigation");
          var formatTime = function (t) {
            return Math.round(1e3 * (t / 1e3 + Number.EPSILON)) / 1e3;
          };
          if (a.length > 0) {
            a = a[0];

            var totalTime = a.loadEventEnd - a.startTime;
            var responseLoadTime = a.loadEventEnd - a.requestStart;
            var domLoadTime = a.loadEventStart - a.responseEnd;
            var domTime = a.domComplete - a.responseEnd;

            // 自定义接口上报  http://beta-cmsnew.wondershare.cn/
            fetch("/api/v1/gta/data", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                page_id: 538184,
                totalTime: formatTime(totalTime),
                responseLoadTime: formatTime(responseLoadTime),
                domLoadTime: formatTime(domLoadTime),
                domTime: formatTime(domTime),
              }),
            });
          }
        }, 0);
      });
    </script>
  </body>
</html>
